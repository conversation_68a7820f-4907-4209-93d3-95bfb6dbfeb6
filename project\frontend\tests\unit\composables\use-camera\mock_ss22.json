[{"user_agent": "mock_ss22", "slug": "mock_ss22", "created_at": null, "log_camera": {"devices": [{"deviceConstraints": null, "device": {"deviceId": "5e009efbc0a366d153a2fc243d4a02ea7fb6bdf0e4b02f19efa7a50ac8b93dce", "kind": "videoinput", "label": "camera2 2, facing back", "groupId": "64eda62214fd61d55f38156eec092c9328502bae68a24bbe29b7519d55266ae3"}, "tracks": [{"label": "camera2 2, facing back", "id": "1d7fee9f-e9a1-45d2-bcb8-b6d4dec37bfa", "contentHint": "", "kind": "video", "enabled": true, "muted": false, "readyState": "live", "constraints": {"deviceId": {"exact": "5e009efbc0a366d153a2fc243d4a02ea7fb6bdf0e4b02f19efa7a50ac8b93dce"}}, "capabilities": {"aspectRatio": {"max": 4032, "min": 0.00033068783068783067}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "deviceId": "5e009efbc0a366d153a2fc243d4a02ea7fb6bdf0e4b02f19efa7a50ac8b93dce", "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 2067, "min": 0, "step": 0}, "facingMode": ["environment"], "focusDistance": {"max": 0.785714328289032, "min": 0, "step": 0.009999999776482582}, "focusMode": ["manual"], "frameRate": {"max": 30, "min": 0}, "groupId": "64eda62214fd61d55f38156eec092c9328502bae68a24bbe29b7519d55266ae3", "height": {"max": 3024, "min": 1}, "iso": {"max": 3200, "min": 50, "step": 1}, "resizeMode": ["none", "crop-and-scale"], "whiteBalanceMode": ["continuous", "manual"], "width": {"max": 4032, "min": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}}, "settings": {"aspectRatio": 0.75, "colorTemperature": 0, "deviceId": "5e009efbc0a366d153a2fc243d4a02ea7fb6bdf0e4b02f19efa7a50ac8b93dce", "exposureCompensation": 0, "exposureMode": "continuous", "exposureTime": 0, "facingMode": "environment", "focusDistance": 0, "focusMode": "continuous", "frameRate": 30, "groupId": "64eda62214fd61d55f38156eec092c9328502bae68a24bbe29b7519d55266ae3", "height": 640, "iso": 50, "resizeMode": "none", "whiteBalanceMode": "continuous", "width": 480, "zoom": 1}}], "success": true}, {"deviceConstraints": null, "device": {"deviceId": "101e0ea2c6a4008716996673d86e4f8824379238387269c1db608caed9da55f8", "kind": "videoinput", "label": "camera2 0, facing back", "groupId": "93a9aa6edb09aec03b687e16590b7a5a4f7c5ce6f740d9c5412823a82fa4a92d"}, "tracks": [{"label": "camera2 0, facing back", "id": "806168d5-4270-45d6-8030-0635ca5698a9", "contentHint": "", "kind": "video", "enabled": true, "muted": false, "readyState": "live", "constraints": {"deviceId": {"exact": "101e0ea2c6a4008716996673d86e4f8824379238387269c1db608caed9da55f8"}}, "capabilities": {"aspectRatio": {"max": 4080, "min": 0.000326797385620915}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "deviceId": "101e0ea2c6a4008716996673d86e4f8824379238387269c1db608caed9da55f8", "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 1672, "min": 0, "step": 0}, "facingMode": ["environment"], "focusDistance": {"max": 4.0500006675720215, "min": 0.07000000029802322, "step": 0.009999999776482582}, "focusMode": ["manual", "single-shot", "continuous"], "frameRate": {"max": 30, "min": 0}, "groupId": "93a9aa6edb09aec03b687e16590b7a5a4f7c5ce6f740d9c5412823a82fa4a92d", "height": {"max": 3060, "min": 1}, "iso": {"max": 3200, "min": 50, "step": 1}, "resizeMode": ["none", "crop-and-scale"], "torch": true, "whiteBalanceMode": ["continuous", "manual"], "width": {"max": 4080, "min": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}}, "settings": {"aspectRatio": 0.75, "colorTemperature": 0, "deviceId": "101e0ea2c6a4008716996673d86e4f8824379238387269c1db608caed9da55f8", "exposureCompensation": 0, "exposureMode": "continuous", "exposureTime": 399, "facingMode": "environment", "focusDistance": 0, "focusMode": "continuous", "frameRate": 30, "groupId": "93a9aa6edb09aec03b687e16590b7a5a4f7c5ce6f740d9c5412823a82fa4a92d", "height": 640, "iso": 50, "resizeMode": "none", "torch": false, "whiteBalanceMode": "continuous", "width": 480, "zoom": 1}}], "success": true}]}}]