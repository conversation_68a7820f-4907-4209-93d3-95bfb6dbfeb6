/// <reference types="vite/client" />
import fs from 'fs';
import snakeCase from 'lodash/snakeCase';
import path from 'path';
import { afterAll, beforeEach, describe, expect, test, vi } from 'vitest';

import { type MediaDeviceInfoDetailed, useCamera } from '@ekyc/composables/use-camera';

type CameraLog = {
  user_agent: string;
  slug: string;
  created_at: string;
  log_camera: {
    devices: MediaDeviceInfoDetailed[];
  };
};

function setMockGetDetailedVideoDevices(detailedVideoDevices: MediaDeviceInfoDetailed[]) {
  window.navigator = {
    ...navigator,
    mediaDevices: {
      ...navigator.mediaDevices,
      getSupportedConstraints: vi.fn(),
      enumerateDevices: () => detailedVideoDevices.map(d => d.device) as any,
      getUserMedia: (constraints: any) => {
        const device =
          detailedVideoDevices.find(
            dev => dev.device.deviceId === constraints.video?.deviceId?.exact,
          ) ||
          detailedVideoDevices.find(dev =>
            dev.tracks[0].capabilities.facingMode.includes('environment'),
          );

        return {
          getTracks: () =>
            device.tracks.map(track => ({
              ...track,
              getSettings: () => track.settings,
              getCapabilities: () => track.capabilities,
              getConstraints: () => track.constraints,
              applyConstraints: () => vi.fn(),
              stop: vi.fn(),
            })),
        } as any;
      },
    },
  };
}

function getMockVideo() {
  return {
    src: null,
    srcObject: null,
    readyState: 4,
    play: vi.fn(),
    videoWidth: null,
    videoHeight: null,
    width: null,
    height: null,
    clientWidth: null,
    clientHeight: null,
    offsetWidth: null,
    offsetHeight: null,
    autoplay: null,
    getVideoPlaybackQuality: vi.fn(),
    playsInline: null,
    poster: null,
    buffered: null,
    controls: null,
    crossOrigin: null,
    currentSrc: null,
    currentTime: null,
    defaultMuted: null,
    defaultPlaybackRate: null,
    disableRemotePlayback: null,
    duration: null,
    ended: null,
    error: null,
    loop: null,
    mediaKeys: null,
    muted: null,
    paused: null,
    playbackRate: null,
    played: null,
    preload: null,
    preservesPitch: null,
    networkState: null,
    remote: null,
    seekable: null,
    seeking: null,
    volume: null,
    style: null,
  } as unknown as HTMLVideoElement;
}

const jsonFiles = Object.entries(import.meta.glob<{ default: CameraLog[] }>('./mock_*.json'));
const csvFiles = Object.entries(import.meta.glob<{ default: CameraLog[] }>('./*.csv'));

describe('use-camera', () => {
  const mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => undefined);
  const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => undefined);
  const passedSlugs: Record<string, boolean> = {};

  beforeEach(() => {});

  // Process one file at a time instead of all at once
  for (const [filename, importFn] of [...jsonFiles, ...csvFiles]) {
    describe(`CameraLog ${filename}`, async () => {
      // Load the file contents
      const rows = (await importFn()).default;

      test.each(
        rows /* exclude big tests */
          .slice(0, 5),
      )('$user_agent', async row => {
        const log: typeof row.log_camera =
          typeof row.log_camera === 'string' ? JSON.parse(row.log_camera) : row.log_camera;
        log.devices = log.devices.map((device, i) => ({
          ...device,
          device: {
            ...device.device,
            deviceId: `$D${i}`,
            groupId: `$G${i}`,
          },
          tracks: device.tracks?.map((track, j) => ({
            ...track,
            id: `$T${j}`,
            settings: {
              ...track.settings,
              deviceId: `$D${i}`,
            },
            constraints: {
              ...track.constraints,
              deviceId: {
                exact: `$D${i}`,
              },
            },
            capabilities: {
              ...track.capabilities,
              deviceId: `$D${i}`,
            },
          })),
        }));

        setMockGetDetailedVideoDevices(log.devices);

        const { loadCamera } = useCamera({
          videoRef: ref(getMockVideo()),
          highQuality: false,
          startFacingMode: 'environment',
          overrideConstraints: {},
          workingConstraints: ref({}),
        });

        const result = await loadCamera();

        passedSlugs[row.slug] = false;

        // expect(result.success).toBe(true);
        const snapshot = result.logs.attempts.map(att => ({
          deviceMethod: att.deviceMethod,
          resolutionMethod: att.resolutionMethod,
          constraints: att.constraints,
          streamTracks: att.streamTracks,
          reConstraints: att.video?.reConstraints,
          reFocused: att.video?.reFocused,
          reIso: att.video?.reIso,
        }));
        // Generate device-specific filename from user agent
        const ua = snakeCase(row.user_agent).split('_like')[0].slice(0, 100);
        const snapshotFilename = `./__snapshots__/use-camera/${ua}_${row.slug}.snap.json`;
        await expect(JSON.stringify(snapshot, null, 2)).toMatchFileSnapshot(snapshotFilename);

        passedSlugs[row.slug] = true;
      });
    });
  }

  afterAll(() => {
    mockConsoleLog.mockRestore();
    mockConsoleError.mockRestore();

    const a = Object.entries(passedSlugs);
    const f = a.filter(([, value]) => !value).map(([slug]) => slug);
    console.log('======= Failed Slugs =======');
    fs.writeFile(
      path.join(__dirname, './__results__/changed.json'),
      JSON.stringify(
        {
          count: a.length,
          failed: f.length,
          percent: +((f.length / a.length) * 100).toFixed(2),
          slugs: f,
        },
        null,
        2,
      ),
      () => {
        console.log('changed.json saved');
      },
    );
  });
});
