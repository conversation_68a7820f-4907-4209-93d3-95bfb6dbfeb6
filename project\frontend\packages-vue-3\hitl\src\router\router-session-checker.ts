import { useCookies } from '@vueuse/integrations/useCookies';
import { storeToRefs } from 'pinia';

import { useSessionStore } from '@/store/session';
import type { Router } from 'vue-router';

const LOG_PREFIX = [
  '%c[router-session-checker]',
  'background: #222; color: white; border: 1px solid white;',
];

const routerSessionChecker = (router: Router) => {
  router.beforeEach(async (to, from, next) => {
    console.log(...LOG_PREFIX, from?.fullPath, '->', to?.fullPath);

    const SESSION_ID_COOKIE_NAME = 'sessionid';
    const cookies = useCookies([SESSION_ID_COOKIE_NAME]);
    const sessionIdToken = cookies.get<string>(SESSION_ID_COOKIE_NAME);

    const sessionStore = useSessionStore();
    const { isLoggedIn } = storeToRefs(sessionStore);

    if (!isLoggedIn.value && sessionIdToken) {
      await sessionStore.fetchUser();
    }

    if (to.path.includes('login/') && isLoggedIn.value) {
      return next({ name: 'portal' });
    }

    return next();
  });
};

export default routerSessionChecker;
