[{"user_agent": "mock_samsung_browser_24_android_10", "slug": "mock_samsung_browser_24_android_10", "created_at": null, "log_camera": {"devices": [{"deviceConstraints": null, "device": {"deviceId": "13f88f56113fddf184c65c8c55faf1814fc0cb3433cadd8b92f33532657a88e3", "kind": "videoinput", "label": "camera2 2, facing back", "groupId": "8f63ff8e7508a859aaffeeccc478f49d2d0991e9489fd7bf4e961942976edd8a"}, "tracks": [{"label": "camera2 2, facing back", "id": "17864123-6452-4c3c-a12a-cfdba8c5d30e", "contentHint": "", "kind": "video", "enabled": true, "muted": false, "readyState": "live", "constraints": {"deviceId": {"exact": "13f88f56113fddf184c65c8c55faf1814fc0cb3433cadd8b92f33532657a88e3"}}, "capabilities": {"aspectRatio": {"max": 4000, "min": 0.0003333333333333333}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "deviceId": "13f88f56113fddf184c65c8c55faf1814fc0cb3433cadd8b92f33532657a88e3", "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 1000, "min": 0, "step": 0}, "facingMode": ["environment"], "focusDistance": {"max": 3.5999999046325684, "min": 0, "step": 0.009999999776482582}, "focusMode": ["manual"], "frameRate": {"max": 30, "min": 0}, "groupId": "8f63ff8e7508a859aaffeeccc478f49d2d0991e9489fd7bf4e961942976edd8a", "height": {"max": 3000, "min": 1}, "iso": {"max": 3200, "min": 50, "step": 1}, "resizeMode": ["none", "crop-and-scale"], "whiteBalanceMode": ["continuous", "manual"], "width": {"max": 4000, "min": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}}, "settings": {"aspectRatio": 0.75, "colorTemperature": 0, "deviceId": "13f88f56113fddf184c65c8c55faf1814fc0cb3433cadd8b92f33532657a88e3", "exposureCompensation": 0, "exposureMode": "continuous", "exposureTime": 0, "facingMode": "environment", "focusDistance": 0, "focusMode": "continuous", "frameRate": 30, "groupId": "8f63ff8e7508a859aaffeeccc478f49d2d0991e9489fd7bf4e961942976edd8a", "height": 640, "iso": 0, "resizeMode": "none", "whiteBalanceMode": "continuous", "width": 480, "zoom": 1}}], "success": true}, {"deviceConstraints": null, "device": {"deviceId": "454af0e142acbb67a981806c720c9eefea61b82165ef6bb21ca0df14663073c1", "kind": "videoinput", "label": "camera2 0, facing back", "groupId": "c6b4e2f46ccf63ecf59645dc87dbd5d84a4962e0df1da3542e414bf3d020ec2b"}, "tracks": [{"label": "camera2 0, facing back", "id": "45df81e4-3df5-43d2-95f8-20ee226ebd3b", "contentHint": "", "kind": "video", "enabled": true, "muted": false, "readyState": "live", "constraints": {"deviceId": {"exact": "454af0e142acbb67a981806c720c9eefea61b82165ef6bb21ca0df14663073c1"}}, "capabilities": {"aspectRatio": {"max": 4624, "min": 0.0002883506343713956}, "colorTemperature": {"max": 7000, "min": 2850, "step": 50}, "deviceId": "454af0e142acbb67a981806c720c9eefea61b82165ef6bb21ca0df14663073c1", "exposureCompensation": {"max": 2, "min": -2, "step": 0.10000000149011612}, "exposureMode": ["continuous", "manual"], "exposureTime": {"max": 1000, "min": 0, "step": 0}, "facingMode": ["environment"], "focusDistance": {"max": 3.5999999046325684, "min": 0.10000000149011612, "step": 0.009999999776482582}, "focusMode": ["manual", "single-shot", "continuous"], "frameRate": {"max": 30, "min": 0}, "groupId": "c6b4e2f46ccf63ecf59645dc87dbd5d84a4962e0df1da3542e414bf3d020ec2b", "height": {"max": 3468, "min": 1}, "iso": {"max": 3200, "min": 50, "step": 1}, "resizeMode": ["none", "crop-and-scale"], "torch": true, "whiteBalanceMode": ["continuous", "manual"], "width": {"max": 4624, "min": 1}, "zoom": {"max": 8, "min": 1, "step": 0.1}}, "settings": {"aspectRatio": 0.75, "colorTemperature": 0, "deviceId": "454af0e142acbb67a981806c720c9eefea61b82165ef6bb21ca0df14663073c1", "exposureCompensation": 0, "exposureMode": "continuous", "exposureTime": 0, "facingMode": "environment", "focusDistance": 0, "focusMode": "continuous", "frameRate": 30, "groupId": "c6b4e2f46ccf63ecf59645dc87dbd5d84a4962e0df1da3542e414bf3d020ec2b", "height": 640, "iso": 0, "resizeMode": "none", "torch": false, "whiteBalanceMode": "continuous", "width": 480, "zoom": 1}}], "success": true}]}}]