ALL_LIVENESS_ITEM_TYPES = [
    "Liveness",
    "LivenessV1",
    "LivenessV2",
    "LivenessV3",
    "LivenessV4",
    "LivenessVNext",
]

ALL_DOCUMENT_ITEM_TYPES = [
    "Document",
    "DocumentVNext",
    "FrontCard",
    "Passport",
    "DriverLicense",
    "ResidencePermit",
    "ThaiAlienCard",
    "Portrait",
    "CiPassport",
    "WorkPermitCard",
    "WorkPermitBook",
    "TravelDocument",
    "WhiteCard",
    "BorderPass",
    "MonkCard",
    "ImmigrationCard",
    "OtherDocument",
]

ALL_DOCUMENT_API_TYPES = [
    "front_card",
    "passport",
    "driver_license",
    "residence_permit",
    "thai_alien_card",
    "portrait",
    "ci_passport",
    "work_permit_card",
    "work_permit_book",
    "travel_document",
    "white_card",
    "border_pass",
    "monk_card",
    "immigration_card",
    "other_document",
]

MODEL_NAME_MAP = {
    "liveness": ALL_LIVENESS_ITEM_TYPES,
    "front_card": ALL_DOCUMENT_ITEM_TYPES,
    "back_card": ["BackCard", "BackCardVNext"],
    "facecompare": ["FaceCompare"],
}


def visitor_ip_address(request):
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")

    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


from django.http.request import HttpRequest
from rest_framework import serializers
from .get_form import get_form_settings


def get_additional_header(request: HttpRequest, serializer: serializers.Serializer, form):
    device_type = serializer.data.get("device_type")

    # Add blacklist_key if configured in form settings
    blacklist_key = get_form_settings(form, "ekyc.blacklist.key", None)
    blacklist_header = {"blacklist_key": blacklist_key} if blacklist_key else {}

    return {
        "device_type": device_type,
        "session_ua": serializer.data.get("session_ua"),
        "session_browser_uuid": serializer.data.get("session_browser_uuid"),
        "session_api_uuid": request.session.session_key,
        "session_ip": visitor_ip_address(request),
        **blacklist_header,
    }
