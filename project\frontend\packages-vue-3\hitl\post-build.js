import * as fs from 'node:fs';
import * as path from 'node:path';

const buildDir = '../../../app/templates/hitl/';
const indexPath = path.join(buildDir, 'index.html');

try {
  let content = fs.readFileSync(indexPath, 'utf8');

  // replace href/src="..." with {% static "..." %} (target file format => index-D_Wz2q-S.js)
  content = content.replaceAll(
    /src="\/(assets\/)?([^"]+\.js)"|href="\/(assets\/)?([^"]+\.css)"/g,
    (match, assetFolder1, jsFile, assetFolder2, cssFile) => {
      const fileName = jsFile || cssFile;

      if (fileName) {
        if (match.includes('.css')) {
          return `href="{% static "${fileName}" %}"`;
        }
        if (match.includes('.js')) {
          return `src="{% static "${fileName}" %}"`;
        }
      }
      return match;
    },
  );

  content = content.replace(/<link rel="icon"[^>]+?href="\/vite\.svg"[^>]*?>/, '');

  // Add {% load static %} on top of the file
  content = `{% load static %}\n${content}`;

  // Save edited file
  fs.writeFileSync(indexPath, content, 'utf8');

  console.log(`✅ ${indexPath} successfully modified for Django static template tags.`);
} catch (error) {
  console.error(`❌ Error modifying ${indexPath}:`, error);
}
