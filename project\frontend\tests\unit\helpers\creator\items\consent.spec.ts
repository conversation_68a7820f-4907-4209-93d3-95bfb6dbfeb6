import { beforeEach, describe, expect, test } from 'vitest';

import i18n from '@core/plugins/i18n';

import { CreatorItemConsent } from '@/helpers/creator/items/consent';
import { CreatorSchema } from '@/helpers/creator/schema';

describe('CreatorItemConsent (builder)', () => {
  let item: CreatorItemConsent;

  beforeEach(() => {
    const creatorSchema = new CreatorSchema('test');
    creatorSchema.addChildByType('step');
    const step = creatorSchema.steps[0];
    item = step.createChildFromSchema({ builder: { type: 'consent' } }) as CreatorItemConsent;
  });

  test.each(['en', 'th'])(
    'should build schema to match the previous snapshot, locale=%s',
    async lang => {
      i18n.locale = lang;
      item.doChangedLocaleActions();
      item.syncSchema();
      expect(item.schema).toMatchSnapshot();
    },
  );

  describe('static properties', () => {
    test('must have correct default values', () => {
      item.setSchema({} as any);
      expect(item.enableScrollBottom).toBe(false);
    });

    test('must have correct static class info', () => {
      expect(item.TYPE_ID).toBe('consent');
    });

    test('should have correct TYPE_LABEL', () => {
      expect(CreatorItemConsent.TYPE_LABEL).toBe('Consent');
    });

    test('should have correct TYPE_ICON', () => {
      expect(CreatorItemConsent.TYPE_ICON).toBe('mdi:file-sign');
    });

    test('should have correct TYPE_COLOR', () => {
      expect(CreatorItemConsent.TYPE_COLOR).toBe('#19CEB0');
    });

    test('should have correct PREVIEW_DESC', () => {
      expect(CreatorItemConsent.PREVIEW_DESC).toBe(
        'A tool to display legal agreements or policies with interactive checkboxes for user consent.',
      );
    });
  });

  describe('default properties', () => {
    test('should initialize with empty content', () => {
      expect(item.content).toBe('');
    });

    test('should initialize with empty version', () => {
      expect(item.version).toBe('');
    });

    test('should initialize with default title styling', () => {
      expect(item.titleStyling).toEqual({
        color: {
          enabled: true,
          value: '#000',
        },
      });
    });

    test('should initialize with default content styling', () => {
      expect(item.contentStyling).toEqual({
        backgroundColor: {
          enabled: false,
          value: '#FFF',
        },
        borderColor: {
          enabled: false,
          value: '#EEEEF0',
        },
      });
    });

    test('should initialize with default checkbox info', () => {
      expect(item.checkboxInfos).toEqual([
        {
          label: 'I have read and agreed to the agreement',
          required: true,
        },
      ]);
    });
  });

  describe('fields configuration', () => {
    test('should have correct field configurations', () => {
      expect(item.fields).toHaveProperty('content');
      expect(item.fields).toHaveProperty('version');
      expect(item.fields).toHaveProperty('date');

      expect(item.fields.content.base.layout).toBe('DefaultWrapper');
      expect(item.fields.version.base.layout).toBe('DefaultWrapper');
      expect(item.fields.date.base.layout).toBe('DefaultWrapper');
    });
  });

  describe('setSchema', () => {
    test('must have correct setSchema', () => {
      item.setSchema({
        display: {
          label: 'My title',
        },
        enable_scroll_bottom: true,
        name: 'my_consent',
        title_styling: {
          color: {
            enabled: true,
            value: '#ABC',
          },
        },
        content: 'My content',
        content_styling: {
          background_color: {
            enabled: true,
            value: '#000',
          },
          border_color: {
            enabled: false,
            value: '#AAA',
          },
        },
        type: 'Consent',
        version: 'v1.2.3',
        items: {
          my_consent_checkbox_1: {
            name: 'my_consent_checkbox_1',
            boolean: true,
            display: {
              hide_label: true,
              label: 'Checkbox A',
            },
            enum: [
              {
                label: 'Checkbox A',
                value: true,
              },
            ],
            type: 'InputCheckbox',
            validator_rule: 'required',
          },
          my_consent_checkbox_2: {
            name: 'my_consent_checkbox_2',
            boolean: true,
            display: {
              hide_label: true,
              label: 'Checkbox B',
            },
            enum: [
              {
                label: 'Checkbox B',
                value: true,
              },
            ],
            type: 'InputCheckbox',
            validator_rule: undefined,
          },
        },
      });

      expect(item.content).toBe('My content');
      expect(item.version).toBe('v1.2.3');
      expect(item.enableScrollBottom).toBe(true);
      expect(item.titleStyling.color.enabled).toBe(true);
      expect(item.titleStyling.color.value).toBe('#ABC');
      expect(item.contentStyling.backgroundColor.enabled).toBe(true);
      expect(item.contentStyling.backgroundColor.value).toBe('#000');
      expect(item.contentStyling.borderColor.enabled).toBe(false);
      expect(item.contentStyling.borderColor.value).toBe('#AAA');
      expect(item.checkboxInfos).toEqual([
        {
          label: 'Checkbox A',
          required: true,
        },
        {
          label: 'Checkbox B',
          required: false,
        },
      ]);
    });

    test('should handle freshly-created schema with default values', () => {
      const freshSchema = {} as Types.ISchemaItemConsent;
      item.setSchema(freshSchema);

      // Should use default values when schema properties are undefined
      expect(item.content).toBe('');
      expect(item.version).toBe('');
      expect(item.enableScrollBottom).toBe(false);
      expect(item.titleStyling.color.enabled).toBe(true);
      expect(item.titleStyling.color.value).toBe('#000');
      expect(item.contentStyling.backgroundColor.enabled).toBe(false);
      expect(item.contentStyling.backgroundColor.value).toBe('#FFF');
      expect(item.contentStyling.borderColor.enabled).toBe(false);
      expect(item.contentStyling.borderColor.value).toBe('#EEEEF0');
      expect(item.checkboxInfos).toEqual([
        {
          label: 'I have read and agreed to the agreement',
          required: true,
        },
      ]);
    });

    test('should handle schema with no items', () => {
      const schemaWithNoItems = {
        content: 'Test Content',
        version: '1.0.0',
        enable_scroll_bottom: true,
        title_styling: {
          color: {
            enabled: false,
            value: '#111111',
          },
        },
        content_styling: {
          background_color: {
            enabled: true,
            value: '#222222',
          },
          border_color: {
            enabled: true,
            value: '#333333',
          },
        },
        items: {},
      } as Types.ISchemaItemConsent;

      item.setSchema(schemaWithNoItems);

      expect(item.content).toBe('Test Content');
      expect(item.version).toBe('1.0.0');
      expect(item.enableScrollBottom).toBe(true);
      expect(item.titleStyling.color.enabled).toBe(false);
      expect(item.titleStyling.color.value).toBe('#111111');
      expect(item.contentStyling.backgroundColor.enabled).toBe(true);
      expect(item.contentStyling.backgroundColor.value).toBe('#222222');
      expect(item.contentStyling.borderColor.enabled).toBe(true);
      expect(item.contentStyling.borderColor.value).toBe('#333333');
      // Should keep default checkbox when no items provided
      expect(item.checkboxInfos).toEqual([
        {
          label: 'I have read and agreed to the agreement',
          required: true,
        },
      ]);
    });

    test('should handle schema with multiple checkbox items', () => {
      const schemaWithItems = {
        content: 'Test Content',
        version: '2.0.0',
        type: 'Consent',
        enable_scroll_bottom: true,
        name: 'my_consent',
        items: {
          test_checkbox_1: {
            name: 'test_checkbox_1',
            display: {
              label: 'First Checkbox',
              hide_label: true,
            },
            type: 'InputCheckbox',
            boolean: true,
            validator_rule: 'required',
          },
          test_checkbox_2: {
            name: 'test_checkbox_2',
            display: {
              label: 'Second Checkbox',
              hide_label: true,
            },
            type: 'InputCheckbox',
            boolean: true,
          },
          test_checkbox_3: {
            name: 'test_checkbox_3',
            display: {
              label: 'Third Checkbox',
              hide_label: true,
            },
            type: 'InputCheckbox',
            boolean: true,
            validator_rule: 'required',
          },
        },
      } satisfies Types.ISchemaItemConsent;

      item.setSchema(schemaWithItems);

      // Should extract checkbox information from items
      expect(item.checkboxInfos).toEqual([
        {
          label: 'First Checkbox',
          required: true,
        },
        {
          label: 'Second Checkbox',
          required: false,
        },
        {
          label: 'Third Checkbox',
          required: true,
        },
      ]);

      // Other properties should still be set correctly
      expect(item.content).toBe('Test Content');
      expect(item.version).toBe('2.0.0');
    });

    test('should handle partial schema updates', () => {
      // First set a complete schema
      item.setSchema({
        content: 'Initial Content',
        version: '1.0.0',
        enable_scroll_bottom: true,
        title_styling: {
          color: {
            enabled: true,
            value: '#111111',
          },
        },
        content_styling: {
          background_color: {
            enabled: true,
            value: '#222222',
          },
          border_color: {
            enabled: true,
            value: '#333333',
          },
        },
      } as Types.ISchemaItemConsent);

      // Then update with partial schema
      item.setSchema({
        content: 'Updated Content',
        title_styling: {
          color: {
            value: '#444444',
          },
        },
      } as Types.ISchemaItemConsent);

      // Check that only specified properties were updated
      expect(item.content).toBe('Updated Content');
      expect(item.version).toBe('1.0.0'); // Unchanged
      expect(item.enableScrollBottom).toBe(true); // Unchanged
      expect(item.titleStyling.color.enabled).toBe(true); // Unchanged
      expect(item.titleStyling.color.value).toBe('#444444'); // Updated
      expect(item.contentStyling.backgroundColor.enabled).toBe(true); // Unchanged
      expect(item.contentStyling.backgroundColor.value).toBe('#222222'); // Unchanged
      expect(item.contentStyling.borderColor.enabled).toBe(true); // Unchanged
      expect(item.contentStyling.borderColor.value).toBe('#333333'); // Unchanged
    });
  });

  describe('syncSchema', () => {
    test('should sync basic properties correctly', () => {
      item.content = 'Test content';
      item.version = '1.0.0';
      item.enableScrollBottom = true;
      item.syncSchema();

      expect(item.schema.content).toBe('Test content');
      expect(item.schema.version).toBe('1.0.0');
      expect(item.schema.enable_scroll_bottom).toBe(true);
    });

    test('should sync styling properties correctly', () => {
      item.titleStyling.color.value = '#FF0000';
      item.contentStyling.backgroundColor.enabled = true;
      item.contentStyling.backgroundColor.value = '#00FF00';
      item.syncSchema();

      expect(item.schema.title_styling?.color?.value).toBe('#FF0000');
      expect(item.schema.content_styling?.background_color?.enabled).toBe(true);
      expect(item.schema.content_styling?.background_color?.value).toBe('#00FF00');
    });

    test('should sync multiple checkboxes correctly', () => {
      item.checkboxInfos = [
        { label: 'Checkbox 1', required: true },
        { label: 'Checkbox 2', required: false },
      ];
      item.syncSchema();

      expect(item.schema.items).toHaveProperty(`${item.name}_checkbox_1`);
      expect(item.schema.items).toHaveProperty(`${item.name}_checkbox_2`);
      expect(item.schema.items[`${item.name}_checkbox_1`].validator_rule).toBe('required');
      expect(item.schema.items[`${item.name}_checkbox_2`].validator_rule).toBeUndefined();
    });
  });
});
