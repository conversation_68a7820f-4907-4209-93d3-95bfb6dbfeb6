/* @import 'tailwindcss'; */
@import 'tw-animate-css';

@layer theme, custom-base, custom-components, custom-utilities;
@import 'tailwindcss/theme.css' layer(theme);
@import 'tailwindcss/preflight.css' layer(custom-base);
@import 'tailwindcss/utilities.css' layer(custom-utilities);

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* @layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
} */

:root {
  font-family: 'Manrope', sans-serif, system-ui, Avenir, Helvetica, Arial;

  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: rgba(67, 100, 232, 1);
  --primary-foreground: oklch(0.985 0 0);
  --primary-background-light: rgba(236, 239, 253, 1);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: rgba(67, 100, 232, 1);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: rgba(67, 100, 232, 1);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: rgba(217, 224, 250, 1);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: rgba(67, 100, 232, 1);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: rgba(67, 100, 232, 1);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  --color-text: rgba(42, 48, 73, 1);
  --color-text-light: rgba(119, 123, 139, 1);
  --color-text-lightest: rgba(187, 189, 197, 1);

  --color-success-rgb: 25, 206, 176;
  --color-success: rgba(var(--color-success-rgb), 1);
  --color-success-dark: rgba(29, 170, 151, 1);
  --color-warning-rgb: 255, 226, 124;
  --color-warning: rgba(var(--color-warning-rgb), 1);
  --color-warning-dark: rgba(212, 190, 114, 1);
  --color-danger-rgb: 237, 104, 152;
  --color-danger: rgba(var(--color-danger-rgb), 1);
  --color-danger-dark: rgba(198, 93, 136, 1);

  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: rgba(67, 100, 232, 1);
  --primary-foreground: oklch(0.205 0 0);
  --primary-background-light: rgba(236, 239, 253, 1);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: rgba(67, 100, 232, 1);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: rgba(67, 100, 232, 1);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: rgba(67, 100, 232, 1);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: rgba(67, 100, 232, 1);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: rgba(67, 100, 232, 1);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);

  --color-text: #fff;
  --color-text-light: rgba(119, 123, 139, 1);
  --color-text-lightest: rgba(187, 189, 197, 1);

  --color-success-rgb: 25, 206, 176;
  --color-success: rgba(var(--color-success-rgb), 1);
  --color-success-dark: rgba(29, 170, 151, 1);
  --color-warning-rgb: 255, 226, 124;
  --color-warning: rgba(var(--color-warning-rgb), 1);
  --color-warning-dark: rgba(212, 190, 114, 1);
  --color-danger-rgb: 237, 104, 152;
  --color-danger: rgba(var(--color-danger-rgb), 1);
  --color-danger-dark: rgba(198, 93, 136, 1);
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  padding: unset !important;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: var(--color-text);
    background-color: var(--background);
  }
  a:hover {
    color: var(--primary);
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Card Component */
div[data-slot='card'] {
  padding: unset !important;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  gap: unset;
  box-shadow: unset;

  div[data-slot='card-header'] {
    height: 40px;
    padding: 8px 16px !important;
    border-bottom: 1px solid var(--color-border);
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    background-color: var(--primary-background-light);

    h3[data-slot='card-title'] {
      font-weight: 700;
      font-size: 13px;
      line-height: 24px;
      text-align: left;
    }
  }
}

.text-primary {
  color: var(--color-primary);
}

.text-light {
  color: var(--color-text-light);
}

.text-lightest {
  color: var(--color-text-lightest);
}

.text-normal {
  color: var(--color-text);
}

.text-success {
  color: var(--color-success);
}

.text-warning {
  color: rgba(255, 133, 26, 1);
}

.text-danger {
  color: var(--color-danger);
}

[data-rich-colors='true'][data-sonner-toast][data-type='error'] {
  background: var(--color-danger) !important;
  border-color: var(--color-danger) !important;
  color: var(--color-text) !important;
  font-size: 13px;
}
