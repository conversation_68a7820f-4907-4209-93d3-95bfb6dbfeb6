from django.test import TestCase
from ..models import *


class TriggerTest(TestCase):
    def test_trigger_custom_method_(self):
        webhook = Webhook(
            name = 'test',
            event_trigger = 'submit',
            method = Webhook.Method.CUSTOM
        )
        webhook.save()
        key = 'SECRET'
        method = {
            'authorization' : Trigger.Authorization.BEARER_TOKEN,
            'url' : 'https://us-central1-credit-ok-testing.cloudfunctions.net/test_only',
            'url_get_token' : 'https://us-central1-credit-ok-testing.cloudfunctions.net/test_only',
            'username' : 'username',
            'password' : 'password'
        }
        webhook.add_method(
            method = Webhook.Method.CUSTOM, 
            key = key,
            **method
        )

        body = {
            'test': 'submit'
        }
        webhook.create_task()
        webhook.trigger(body=body, key=key)
    
    def test_encrypt(self):
        method = CustomMethod(password='abc') #NOSONAR
        method.encrypt_password(key='key')
        ciphertext = method.password
        self.assertNotEqual(ciphertext, 'abc')
        plaintext = method.decrypt_password(key='key')
        self.assertEqual(plaintext, 'abc')

