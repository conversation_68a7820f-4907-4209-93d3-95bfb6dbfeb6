let STORAGE: Storage;
const VueSession = {
  key: 'vue-session-key',
  setAll(all: unknown) {
    STORAGE.setItem(VueSession.key, JSON.stringify(all));
  },
};

class SessionManager {
  constructor(persist: boolean) {
    STORAGE = persist ? globalThis.localStorage : globalThis.sessionStorage;
  }

  setAll(all: unknown) {
    STORAGE.setItem(VueSession.key, JSON.stringify(all));
  }

  getAll() {
    const all = JSON.parse(STORAGE.getItem(VueSession.key)!);
    return all || {};
  }

  set(key: string, value: unknown) {
    if (key === 'session-id') return;
    let all = this.getAll();

    if (!('session-id' in all)) {
      this.start();
      all = this.getAll();
    }

    all[key] = value;

    VueSession.setAll(all);
  }

  get(key: string) {
    const all = this.getAll();
    return all[key];
  }

  start() {
    const all = this.getAll();
    all['session-id'] = `sess:${Date.now()}`;

    VueSession.setAll(all);
  }

  renew(sessionId: string) {
    const all = this.getAll();
    all['session-id'] = `sess:${sessionId}`;
    VueSession.setAll(all);
  }

  exists() {
    const all = this.getAll();
    return 'session-id' in all;
  }

  has(key: string) {
    const all = this.getAll();
    return key in all;
  }

  remove(key: string) {
    const all = this.getAll();
    delete all[key];

    VueSession.setAll(all);
  }

  clear() {
    const all = this.getAll();

    VueSession.setAll({ 'session-id': all['session-id'] });
  }

  destroy() {
    VueSession.setAll({});
  }

  id() {
    return this.get('session-id');
  }
}

export default new SessionManager(true);
