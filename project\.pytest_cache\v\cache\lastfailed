{"appsetting/tests/test_api.py::SettingsTests::test_get_list_for_admin": true, "bankstatement/tests/test_validate_document.py::BankStatementDocumentTests::test_validate_document": true, "dynamicform/tests/submodules/form/functions/test_assign_user.py::AssignUserTest::test_auto_assign_user_after_create_form": true, "ekyc/tests/test_form.py::EkycTests::test_get_report_preview_url": true, "ekyc/tests/test_form_front.py::EkycTests::test_passport_mrz": true, "ekyc/tests/test_form_passport.py::EkycTests::test_passport": true, "ekyc/tests/test_preview_url.py::EkycTests::test_get_report_preview_url": true, "ekyc/tests/test_result.py::EkycResultTests::test_result_flow": true, "ndid/tests/test_api.py::NdidTests::test_failed_flow": true, "ndid/tests/test_api.py::NdidTests::test_get_idps": true, "ndid/tests/test_api.py::NdidTests::test_success_flow": true, "workspace/tests/test_checkout.py::TestCheckOutTestCase::test_checkout_can_create_transaction": true, "workspace/tests/test_checkout.py::TestCheckOutTestCase::test_checkout_response_error": true, "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_callback_charge_success": true, "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_callback_payment_intent_success": true, "workspace/tests/test_checkout_auto_renew_callback.py::TestCheckOutTestCase::test_auto_renew_payment": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_auto_renew_trigger": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_1_credit": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_3_credit": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_credit_for_calculate_alert_email": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_expired_flow_2_over_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow_2_over_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_flow_over_1_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_no_credit": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_credit_usage_send_email": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_success": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_payment_intent_created": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_payment_intent_success": true, "workspace/tests/test_credit_usage_history.py::TestUpdateCreditUsage::test_auto_renew_callback_charge_success": true, "ekyc/tests/test_ocr_extractor.py::EkycTests::test_fullname_but_no_firstname_lastname": true, "decision_flow/tests/compiler/test_compile.py::CompileTest::test_compile_simple": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_NOT_FOUND-test_case_input11-expected_result11]": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ALL_CORRUPTION_FOUND-test_case_input14-expected_result14]": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input15-expected_result15]": true, "data_point/tests/provider/test_led.py::TestLED::test_led_data_point_options_result[ANY_CORRUPTION_FOUND-test_case_input11-expected_result11]": true, "smartuploader/tests/test_callback.py::CallbackTest::test_callback_no_editor_permission": true, "smartuploader/tests/test_callback.py::CallbackTest::test_callback_not_in_workspace": true, "smartuploader/tests/test_result.py::ResultTest::test_trigger_smart_uploader_called": true, "smartuploader/tests/test_model.py::ModelTest::test_info_complete": true, "smartuploader/tests/test_model.py::ModelTest::test_info_passed": true, "ekyc/tests/test_back.py::BackCardTests::test_api_report": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_call_charge_success_then_checkout_session_completed": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_call_checkout_session_completed_then_charge_success": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_get_payment_intent_id_from_event": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_ignore_update_transaction_status": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_paid_package": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_fail": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_charge_update": true, "workspace/tests/test_checkout_callback.py::TestCheckOutTestCase::test_response_event_checkout_session_completed": true, "workspace/tests/test_permissions.py::PermissionTests::test_api_create_edit_duplicate_flow": true, "ekyc/tests/test_form_passport.py::EkycTests::test_passport_nfc": true, "ekyc/tests/test_form_passport.py::EkycTests::test_passport_nfc_pass": true, "ekyc/tests/test_nfc.py::EkycTests::test_passport_nfc_none": true, "ekyc/tests/test_nfc.py::EkycTests::test_passport_nfc_pass": true, "ekyc/tests/test_form_nfc.py::EkycTests::test_passport_nfc_pass": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_fail": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_no_document": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_none": true, "ekyc/tests/test_form_nfc.py::NfcTests::test_nfc_pass": true, "ekyc/tests/test_form_nfc.py::EkycTests::test_other_document_should_not_check_nfc": true, "workspace/tests/test_permissions.py::PermissionTests::test_api_view_ekyc_report_result": true, "dynamicform/tests/submodules/form/views/test_apply_form_with_ekyc_reference.py::ApplyFromWithEkycReferenceTests::test_can_create_with_base64_portrait": true, "dynamicform/tests/test_face_compare_integration.py::FaceCompareIntegrationTests::test_update_face_compare_message": true, "ekyc/tests/test_ocr_extractor.py::EkycTests::test_alien_should_not_expiry": true, "ekyc/tests/test_checker_expiry.py::test_expiry_condition_excluded_document_types": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_valid_document": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_expired_document": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_lifelong_document": true, "ekyc/tests/test_checker_expiry.py::test_expiry_not_found": true, "ekyc/tests/test_checker_expiry.py::test_expiry_not_found_ignored": true, "ekyc/tests/test_checker_expiry.py::test_expiry_check_disabled": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_document_upload_without_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyLivenessTests::test_liveness_upload_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyLivenessTests::test_liveness_upload_without_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_liveness_upload_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_liveness_upload_without_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::BlacklistKeyTests::test_document_upload_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_includes_blacklist_key": true, "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_with_x_forwarded_for": true, "ekyc/tests/test_blacklist_key.py::GetAdditionalHeaderTests::test_get_additional_header_without_blacklist_key": true, "ekyc/tests/test_api_gateway_header_body.py::TestBaseDocument::test_submit_additional_header_settings": true, "ekyc/tests/test_form_front.py::EkycTests::test_front_card_check_expiry": true, "ekyc/tests/test_get_sorted_errors.py::TestGetSortedErrors::test_get_sorted_errors_by_document_type": true, "ekyc/tests/test_get_sorted_errors_simple.py::TestGetSortedErrors::test_legacy_priority_parameter": true}