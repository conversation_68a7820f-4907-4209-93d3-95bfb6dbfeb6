<script setup lang="ts">
import { Toaster } from 'vue-sonner';
import DefaultLayout from './layouts/DefaultLayout.vue';
import 'vue-sonner/style.css';
</script>

<template>
  <DefaultLayout>
    <RouterView v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </RouterView>
    <Toaster :rich-colors="true" />
  </DefaultLayout>
</template>

<style lang="scss" scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
