import json
from typing import List
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from pydash import get
from .ekyc_base_item import EkycBaseItem
from rest_framework import status
from time import sleep
from ..helpers.get_form import check_sandbox_mode, check_authorize_webhook_image_url
from ..helpers.external_api import get_result_liveness, get_current_time

STATUS_PENDING = "pending"
STATUS_STARTED = "started"
STATUS_SUCCESS = "success"
STATUS_FAILED_FRONTEND = "failed_frontend"
STATUS_FAILED_BACKEND = "failed_backend"
STATUS_CANCELLED = "cancelled"

STATUS_CHOICES = [
    (STATUS_PENDING, STATUS_PENDING),
    (STATUS_STARTED, STATUS_STARTED),
    (STATUS_SUCCESS, STATUS_SUCCESS),
    (STATUS_FAILED_FRONTEND, STATUS_FAILED_FRONTEND),
    (STATUS_FAILED_BACKEND, STATUS_FAILED_BACKEND),
    (STATUS_CANCELLED, STATUS_CANCELLED),
]


class Liveness(EkycBaseItem):
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_PENDING, db_index=True)
    face_actions = models.CharField(max_length=191)
    face_actions_expire_at = models.CharField(max_length=191)
    action_sequence = models.CharField(max_length=191, default=None, null=True, blank=True)
    log = models.TextField(null=True)

    @property
    def is_success(self):
        return self.status == STATUS_SUCCESS

    @property
    def document_type(self):
        return "liveness"

    @property
    def webhook_key_type(self):
        return "liveness"

    @property
    def face_actions_dict(self) -> List[str]:
        try:
            if self.face_actions is None:
                raise None
            return json.loads(self.face_actions)
        except Exception:
            return None

    def check_conditions(self, schema=None):
        try:
            result = self.result_response
            if result is None:
                return False

            ignore = get(schema, ["configs", "ignore"], {})
            for key, value in get(result, "result", {}).items():
                passed = value.get("status", False) or ignore.get(key, False)
                if not passed:
                    return False
            return True
        except Exception:
            return False

    def fetch_report(self, is_extra=False, **kwargs):
        # No request id
        if not self.upload_response:
            return None, None

        # Current result
        result = get(self.result_response, "gateway_result")
        api_status = get(self.result_response, "gateway_api_status")

        # SANDBOX -> skip

        if check_sandbox_mode(self.ekyc.ref):
            return result, api_status

        if is_extra and check_authorize_webhook_image_url(self.ekyc.ref):
            return result, api_status

        # Fresh image -> skip
        if self.result_response and not self.is_result_image_expired:
            return result, api_status

        # Refresh result
        success = False
        for i in range(5, 0, -1):
            result, api_status = get_result_liveness(self.upload_response)
            updated_result = self.result_response or {}
            updated_result["gateway_result"] = result
            updated_result["gateway_api_status"] = api_status
            if status.is_success(api_status):
                success = True
                break
            elif i > 1:
                sleep(2)
            else:
                raise Exception("Error getting result, Timeout, Try again later")

        # End retry loop
        key = "gateway_last_success_at" if success else "gateway_last_fail_at"
        updated_result[key] = get_current_time().isoformat(" ")
        Liveness.objects.filter(id=self.id).update(result_response=updated_result)
        # NOTE: Not using self.save() to prevent sending post_save
        # self.result_response = updated_result
        # self.save()

        return result, api_status

    def get_latest_error(self):
        result_response = get(self.result_response, "result", {})
        latest_error = []
        for key, i in result_response.items():
            if i.get("status", None) == False:
                latest_error.append({"message": i.get("message", None), "key": key})
        return latest_error

    def call_webhook(self, action_status=None):
        if not action_status:
            action_status = self.status

        hook_map = {
            STATUS_PENDING: "liveness_pending",
            STATUS_STARTED: "liveness_started",
            STATUS_SUCCESS: "liveness_success",
            STATUS_FAILED_FRONTEND: "liveness_failed",
            STATUS_FAILED_BACKEND: "liveness_failed",
            STATUS_CANCELLED: "liveness_cancelled",
        }
        hook_to_call = hook_map[action_status]

        call_webhook_kwargs = (
            {"payload": {"attempt": self.ekyc.liveness_attempt_count}}
            if hook_to_call not in ["liveness_pending"]
            else {}
        )
        self.ekyc.call_webhook(hook_to_call, **call_webhook_kwargs)

    def get_attempt_sorted_errors(self):
        if self.status == STATUS_FAILED_FRONTEND:
            return ["action_time_out"]

        return self.get_sorted_errors(result_path="result", document_type="liveness")
