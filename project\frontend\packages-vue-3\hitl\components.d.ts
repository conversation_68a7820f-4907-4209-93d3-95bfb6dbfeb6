/* eslint-disable */
// @ts-nocheck
// biome-ignore lint: disable
// oxlint-disable
// ------
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399

export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Button: typeof import('./src/components/ui/button/Button.vue')['default']
    Card: typeof import('./src/components/ui/card/Card.vue')['default']
    CardAction: typeof import('./src/components/ui/card/CardAction.vue')['default']
    CardContent: typeof import('./src/components/ui/card/CardContent.vue')['default']
    CardDescription: typeof import('./src/components/ui/card/CardDescription.vue')['default']
    CardFooter: typeof import('./src/components/ui/card/CardFooter.vue')['default']
    CardHeader: typeof import('./src/components/ui/card/CardHeader.vue')['default']
    CardTitle: typeof import('./src/components/ui/card/CardTitle.vue')['default']
    Input: typeof import('./src/components/ui/input/Input.vue')['default']
    Pagination: typeof import('./src/components/ui/pagination/Pagination.vue')['default']
    PaginationContent: typeof import('./src/components/ui/pagination/PaginationContent.vue')['default']
    PaginationEllipsis: typeof import('./src/components/ui/pagination/PaginationEllipsis.vue')['default']
    PaginationFirst: typeof import('./src/components/ui/pagination/PaginationFirst.vue')['default']
    PaginationItem: typeof import('./src/components/ui/pagination/PaginationItem.vue')['default']
    PaginationLast: typeof import('./src/components/ui/pagination/PaginationLast.vue')['default']
    PaginationNext: typeof import('./src/components/ui/pagination/PaginationNext.vue')['default']
    PaginationPrevious: typeof import('./src/components/ui/pagination/PaginationPrevious.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollArea: typeof import('./src/components/ui/scroll-area/ScrollArea.vue')['default']
    ScrollBar: typeof import('./src/components/ui/scroll-area/ScrollBar.vue')['default']
    Skeleton: typeof import('./src/components/ui/skeleton/Skeleton.vue')['default']
    Sonner: typeof import('./src/components/ui/sonner/Sonner.vue')['default']
    Spinner: typeof import('./src/components/ui/spinner/Spinner.vue')['default']
    Table: typeof import('./src/components/ui/table/Table.vue')['default']
    TableBody: typeof import('./src/components/ui/table/TableBody.vue')['default']
    TableCaption: typeof import('./src/components/ui/table/TableCaption.vue')['default']
    TableCell: typeof import('./src/components/ui/table/TableCell.vue')['default']
    TableEmpty: typeof import('./src/components/ui/table/TableEmpty.vue')['default']
    TableFooter: typeof import('./src/components/ui/table/TableFooter.vue')['default']
    TableHead: typeof import('./src/components/ui/table/TableHead.vue')['default']
    TableHeader: typeof import('./src/components/ui/table/TableHeader.vue')['default']
    TableRow: typeof import('./src/components/ui/table/TableRow.vue')['default']
  }
}
