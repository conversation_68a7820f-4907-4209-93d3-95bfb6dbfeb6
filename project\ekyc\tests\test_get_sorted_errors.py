import json
import unittest
from unittest.mock import patch

from ekyc.models.liveness import Liveness
from ekyc.models.front_card import FrontCard
from ekyc.models.back_card import BackCard


class TestGetSortedErrors(unittest.TestCase):
    """Test get_sorted_errors method using actual model instances"""

    def setUp(self):
        # Mock JSON data that matches the actual file structure
        self.mock_json_priorities = {
            "front_card": ["orientation", "image_quality", "ocr", "supported_country", "face", "blocked", "warning"],
            "liveness": ["face_compare", "liveness_detection", "face"],
            "backcard": ["ocr", "image_quality", "warning", "supported_country", "validity"],
        }

    def test_liveness_get_sorted_errors(self):
        """Test liveness model with JSON priorities"""
        liveness = Liveness()
        liveness.result_response = {
            "result": {
                "face": {"status": False},
                "liveness_detection": {"status": False},
                "face_compare": {"status": False},
                "other_error": {"status": False},
            }
        }

        with patch.object(liveness, "_load_error_priorities", return_value=self.mock_json_priorities):
            sorted_errors = liveness.get_sorted_errors(document_type="liveness")

        # Should prioritize liveness errors first
        expected_order = ["face_compare", "liveness_detection", "face", "other_error"]
        self.assertEqual(sorted_errors, expected_order)

    def test_front_card_get_sorted_errors(self):
        """Test front card model with JSON priorities"""
        front_card = FrontCard()
        front_card.result_response = {
            "data": {
                "result": {
                    "face": {"status": False},
                    "ocr": {"status": False},
                    "orientation": {"status": False},
                    "image_quality": {"status": False},
                    "unknown_error": {"status": False},
                }
            }
        }

        with patch.object(front_card, "_load_error_priorities", return_value=self.mock_json_priorities):
            sorted_errors = front_card.get_sorted_errors(result_path="data.result", document_type="front_card")

        # Should prioritize front_card errors first
        expected_order = ["orientation", "image_quality", "ocr", "face", "unknown_error"]
        self.assertEqual(sorted_errors, expected_order)

    def test_back_card_get_sorted_errors(self):
        """Test back card model with JSON priorities"""
        back_card = BackCard()
        back_card.result_response = {
            "data": {
                "result": {
                    "ocr": {"status": False},
                    "warning": {"status": False},
                    "image_quality": {"status": False},
                    "validity": {"status": False},
                    "random_error": {"status": False},
                }
            }
        }

        with patch.object(back_card, "_load_error_priorities", return_value=self.mock_json_priorities):
            sorted_errors = back_card.get_sorted_errors(result_path="data.result", document_type="backcard")

        # Should prioritize backcard errors first
        expected_order = ["ocr", "image_quality", "warning", "validity", "random_error"]
        self.assertEqual(sorted_errors, expected_order)

    def test_get_attempt_sorted_errors_methods(self):
        """Test the actual methods used by the models"""
        # Test Liveness.get_attempt_sorted_errors()
        liveness = Liveness()
        liveness.result_response = {
            "result": {
                "face": {"status": False},
                "liveness_detection": {"status": False},
                "face_compare": {"status": False},
            }
        }

        with patch.object(liveness, "_load_error_priorities", return_value=self.mock_json_priorities):
            sorted_errors = liveness.get_attempt_sorted_errors()

        # Should use liveness priorities
        expected_order = ["face_compare", "liveness_detection", "face"]
        self.assertEqual(sorted_errors, expected_order)

    def test_explicit_document_type_parameter(self):
        """Test that explicit document_type parameter works"""
        liveness = Liveness()
        liveness.result_response = {
            "result": {
                "face": {"status": False},
                "ocr": {"status": False},
                "orientation": {"status": False},
            }
        }

        with patch.object(liveness, "_load_error_priorities", return_value=self.mock_json_priorities):
            # Use front_card priorities instead of liveness
            sorted_errors = liveness.get_sorted_errors(document_type="front_card")

        # Should use front_card priority order
        expected_order = ["orientation", "ocr", "face"]
        self.assertEqual(sorted_errors, expected_order)

    def test_json_file_not_found(self):
        """Test fallback behavior when JSON file is not found"""
        liveness = Liveness()
        liveness.result_response = {
            "result": {
                "face": {"status": False},
                "liveness_detection": {"status": False},
            }
        }

        with patch.object(liveness, "_load_error_priorities", return_value={}):
            sorted_errors = liveness.get_sorted_errors(document_type="liveness")

        # Should return all errors in original order when JSON loading fails
        self.assertEqual(len(sorted_errors), 2)
        self.assertIn("face", sorted_errors)
        self.assertIn("liveness_detection", sorted_errors)

    def test_unknown_document_type(self):
        """Test behavior with unknown document type"""
        liveness = Liveness()
        liveness.result_response = {
            "result": {
                "face": {"status": False},
                "liveness_detection": {"status": False},
            }
        }

        with patch.object(liveness, "_load_error_priorities", return_value=self.mock_json_priorities):
            sorted_errors = liveness.get_sorted_errors(document_type="unknown_type")

        # Should return all errors in original order for unknown document type
        self.assertEqual(len(sorted_errors), 2)
        self.assertIn("face", sorted_errors)
        self.assertIn("liveness_detection", sorted_errors)


if __name__ == "__main__":
    unittest.main()
