<template>
  <div class="ekyc-content-validate">
    <!-- Preview content -->
    <div class="flex flex-col items-center gap-6">
      <div class="header-title">{{ t('ekyc.document.result_header') }}</div>
      <div class="card-preview-content">
        <template v-for="(cardItem, key) in preview">
          <EkycContentValidateItem
            v-if="
              (key === 'backcard' && includeBackcard && (cardItem.isLoading || cardItem.url)) ||
              key === 'document'
            "
            :media="key"
            :item-type="cardItem?.itemType"
            :state="cardItem"
            :is-loading="isLoadingPreviews[key]"
            :is-auto-detect-document-type="isAutoDetectDocumentType"
            :document-type-from-auto-detect="documentTypeFromAutoDetect"
            @trigger-notice="emit('trigger-notice')"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import useDynamicForm from '@core/composables/use-dynamic-form';

import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import EkycContentValidateItem from '../EkycContentValidateItem/EkycContentValidateItem.vue';

type DocumentMediaItemTypes = Exclude<Types.EkycMediaTypes, 'liveness'>;

const emit = defineEmits<(e: 'trigger-notice') => void>();

const props = defineProps({
  media: {
    type: String as () => Types.EkycMediaTypes,
    required: true,
  },
  preview: {
    type: Object as () => Record<DocumentMediaItemTypes, Types.PreviewType>,
    required: true,
  },
  includeBackcard: {
    type: Boolean,
    required: false,
  },
  isAutoDetectDocumentType: {
    type: Boolean,
    required: false,
    default: false,
  },
  documentTypeFromAutoDetect: {
    type: String as () => Types.EkycDocumentItemTypes,
    required: false,
  },
});

const { t } = useI18n();
const { dynamicFormInstance } = useDynamicForm(inject<string>('id'));

const originalShowStepBar = ref<boolean>(false);

const isLoadingPreviews = computed<Record<DocumentMediaItemTypes, boolean>>(() => ({
  document: props.preview?.document?.isLoading,
  backcard: props.preview?.backcard?.isLoading,
}));

const restoreShowStepBarSetting = () => {
  if (dynamicFormInstance) {
    dynamicFormInstance.stepbar.stepbarConfig.show_stepbar = originalShowStepBar.value;
  }
};
onUnmounted(() => {
  restoreShowStepBarSetting();
});

onMounted(() => {
  originalShowStepBar.value = !!dynamicFormInstance?.stepbar?.stepbarConfig?.show_stepbar;
});
</script>

<style scoped lang="scss">
.result-failed {
  min-height: calc(100vh - 312px);
}

.notice-message > .notice-message__text {
  @apply text-sm font-medium;
  color: var(--color-text-light, #777b8b);
}

.header-title {
  @apply text-[22px] font-bold leading-[30px];
}

.success-check {
  @apply flex items-center justify-center h-[72px] w-[72px] rounded-full;
  background-color: var(--color-secondary, #19ceb0);
  position: absolute !important;
  left: calc(50% - 36px);
  top: calc(50% - 36px);
  .icon {
    font-size: 56px;
    width: 56px;
    height: auto;
    color: white;
  }
}

.failed-content {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &.danger {
    background-color: rgba(237, 104, 152, 0.6);
  }

  &.success {
    background-color: rgba(25, 206, 176, 0.4);
  }

  .v-next-ekyc-failed {
    min-height: unset;
    background-color: white;
    height: calc(100% - 48px);
    width: calc(100% - 48px);
    border-radius: 8px;
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 16px;

    :deep(.result-image) {
      min-height: unset !important;
      max-height: 80px !important;
      figure {
        max-height: 80px !important;
        margin: unset !important;
      }
    }
    :deep(.result-message) {
      .title {
        line-height: 26px;
        margin: unset !important;
      }
    }
  }
}
.card-preview-content {
  gap: 24px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

@media (max-width: 390px) {
  .v-next-ekyc-failed {
    :deep(.preview-wrapper) .preview {
      width: 60px !important;
      height: 60px !important;
    }
    :deep(.result-message) .title {
      font-size: 16px !important;
    }

    :deep(.result-message) .notice-message {
      padding: unset;
      .notice-message__text {
        font-size: 12px !important;
      }
    }
  }
}
</style>
