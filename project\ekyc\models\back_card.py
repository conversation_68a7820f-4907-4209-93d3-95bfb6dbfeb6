import re
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from time import sleep
from pydash import get, set_
from .ekyc_base_item import EkycBaseItem
from rest_framework import status
from ..helpers.get_form import check_sandbox_mode, check_authorize_webhook_image_url, check_pii_protection
from ..helpers.external_api import get_result_back_card, get_current_time
from ..helpers.document_checker import check_by_schema_conditions

BACKCARD_MOCKUP_IMAGE = "https://cdn.uppass.io/projects/uppass/img/id_document/thai_backcard.png"


class BackCard(EkycBaseItem):
    is_success = models.BooleanField(default=False, db_index=True)
    nonce = models.CharField(max_length=5, db_index=True, null=True)

    @property
    def document_type(self):
        return "back_card"

    @property
    def webhook_key_type(self):
        return "identity_document_back"

    def check_conditions(self, schema=None):
        try:
            result = self.result_response
            if result is None:
                return False

            pass_all_checks, pass_obj, log_obj = check_by_schema_conditions(
                schema=schema, response=result, with_ocr=False
            )
            return pass_all_checks
        except Exception:
            return False

    def fetch_report(self, is_extra=False, **kwargs):
        # No request id
        if not self.upload_response:
            return None, None

        # Current result
        result = get(self.result_response, "gateway_result")
        api_status = get(self.result_response, "gateway_api_status")

        # SANDBOX -> skip
        if check_sandbox_mode(form=self.ekyc.form):
            return result, api_status

        if is_extra and check_authorize_webhook_image_url(self.ekyc.ref):
            return result, api_status

        # PII Protection -> skip
        if check_pii_protection(form=self.ekyc.form):
            return result, api_status

        # # :todo rm ocr back_card

        # Fresh image -> skip
        if self.result_response and not self.is_result_image_expired:
            return result, api_status

        # Refresh result
        success = False
        for i in range(5, 0, -1):
            result, api_status = get_result_back_card(self.upload_response)
            updated_result = self.result_response
            updated_result["gateway_result"] = result
            updated_result["gateway_api_status"] = api_status
            if status.is_success(api_status):
                success = True
                break
            elif i > 1:
                sleep(2)
            else:
                raise TimeoutError("Error getting result, Timeout, Try again later")

        key = "gateway_last_success_at" if success else "gateway_last_fail_at"
        updated_result[key] = get_current_time().isoformat(" ")
        BackCard.objects.filter(id=self.id).update(result_response=updated_result)
        # NOTE: Not using self.save() to prevent sending post_save
        # self.result_response = updated_result
        # self.save()

        return result, api_status

    def call_webhook(self, is_success=None):
        if not is_success:
            is_success = self.is_success
        hook_to_call = "back_card_success" if is_success else "back_card_failed"
        self.ekyc.call_webhook(hook_to_call, payload={"attempt": self.ekyc.back_card_attempt_count})

    def get_attempt_sorted_errors(self):
        return self.get_sorted_errors(result_path="data.result", document_type="backcard")

    @staticmethod
    def get_result_response_to_create(response: dict, ekyc):

        if not check_pii_protection(form=ekyc.form):
            # not protect PII
            return response

        def get_mask_text(text):
            text = re.sub(r"\d", "0", text)  # digits -> 0
            text = re.sub(r"[A-Za-z]", "X", text)  # ASCII letters -> X
            return text

        # protect PII
        """
        ocr.laser_number
        data.image_url.img
        data.gateway_result.document.raw
        data.gateway_result.document.extracted.laser_number
        gateway_result.result.document.raw
        gateway_result.result.document.extracted.laser_number
        gateway_result.image_url.img
        """
        back_card_response = response.copy()

        # ocr.laser_number
        laser_number = get(back_card_response, "ocr.laser_number", "")
        set_(back_card_response, "ocr.laser_number", get_mask_text(laser_number))

        # data.image_url.img
        set_(back_card_response, "data.image_url.img", BACKCARD_MOCKUP_IMAGE)

        # data.gateway_result.document.raw
        set_(back_card_response, "data.gateway_result.document.raw", "")

        # data.gateway_result.document.extracted.laser_number
        laser_number = get(back_card_response, "data.gateway_result.document.extracted.laser_number", "")
        set_(back_card_response, "data.gateway_result.document.extracted.laser_number", get_mask_text(laser_number))

        # gateway_result.result.document.raw
        set_(back_card_response, "gateway_result.result.document.raw", "")

        # gateway_result.result.document.extracted.laser_number
        laser_number = get(back_card_response, "gateway_result.result.document.extracted.laser_number", "")
        set_(back_card_response, "gateway_result.result.document.extracted.laser_number", get_mask_text(laser_number))

        # gateway_result.image_url.img
        set_(back_card_response, "gateway_result.image_url.img", BACKCARD_MOCKUP_IMAGE)

        return back_card_response
