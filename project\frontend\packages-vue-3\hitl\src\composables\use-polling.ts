interface PollingResult<T> {
  data: Ref<T | null>;
  isPolling: Ref<boolean>;
  stopPolling: () => void;
  startPolling: () => void;
}

/**
 * Executes an async function (fetcher) repeatedly at a specified interval.
 * @param fetcher The async function that fetches the data.
 * @param interval The interval in milliseconds (e.g., 5000 for 5 seconds).
 * @param initialData The initial value for the data ref.
 * @returns An object containing the reactive data, polling status, and control functions.
 */
export const usePolling = <T>(
  fetcher: () => Promise<T>,
  interval: number,
  initialData: Ref<T>,
): PollingResult<T> => {
  const data = initialData;

  const isPolling = ref(false);
  let timer: number | null = null;

  const fetchData = async () => {
    try {
      data.value = await fetcher();
    } catch (error) {
      console.error('Polling failed:', error);
    }
  };

  const startPolling = () => {
    if (timer) {
      return;
    }

    isPolling.value = true;
    fetchData();
    timer = setInterval(fetchData, interval) as unknown as number;
  };

  const stopPolling = () => {
    if (timer !== null) {
      clearInterval(timer);
      timer = null;
      isPolling.value = false;
    }
  };

  onUnmounted(stopPolling);

  return {
    data,
    isPolling,
    stopPolling,
    startPolling,
  };
};
