<template>
  <div class="navbar-wrapper">
    <div class="navbar-content">
      <!-- Tab Navigation -->
      <div class="tab-navigation-wrapper">
        <div
          class="tab-item cursor-default! py-0! h-6! content-start"
          style="margin-right: 24px !important"
        >
          Task :
        </div>
        <div class="tab-navigation-action">
          <div v-for="tab in TABS" :key="tab.value" class="tab-item" @click="onSelectTab(tab)">
            <span class="tab-label" :class="[activeTab === tab.value ? 'active' : 'inactive']">
              {{ tab.label }}
            </span>

            <div v-if="activeTab === tab.value" class="active-tab-line" />
          </div>
        </div>
      </div>

      <!-- Metadata & Remaining task -->
      <div class="flex items-center space-x-6">
        <!-- Metadata -->
        <template v-if="activeTab === 'portal'">
          <div class="meta-data-items">
            <div class="meta-data-item">
              <span class="title">ID :</span>
              <span class="detail" @click="navigateToList(currentTask?.id)">
                {{ currentTask?.id ?? '-' }}
              </span>
            </div>
            <div class="meta-data-item">
              <span class="title">Slug :</span>
              <span class="detail" @click="navigateToList(currentTask?.slug)">
                {{ currentTask?.slug ?? '-' }}
              </span>
            </div>
            <div class="meta-data-item">
              <span class="title">Form Slug :</span>
              <span class="detail" @click="navigateToList(currentTask?.form_slug)">
                {{ currentTask?.form_slug ?? '-' }}
              </span>
            </div>
          </div>

          <!-- Timestamp Block -->
          <div class="timestamp-wrapper">
            <div class="timestamp-item">
              <span class="title">Created at:</span>
              <span class="time-value"> {{ getDateDisplay(currentTask?.created_at) }} </span>
            </div>
            <div class="timestamp-item">
              <span class="title">Expire at:</span>
              <span class="time-value"> {{ getDateDisplay(currentTask?.expired_at) }} </span>
            </div>
          </div>
        </template>

        <!-- Remaining task -->
        <div class="task-remaining-wrapper">Tasks remaining: {{ remainingTaskCount ?? '-' }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { toEngDateTime } from '@/helpers/utils';
import router from '@/router';
import { useTaskStore } from '@/store/task';
import { storeToRefs } from 'pinia';

/* Types */
type TabObject = {
  label: string;
  value: Types.TabValueType;
};

/* Constants */
const TABS: TabObject[] = [
  {
    label: 'List',
    value: 'list',
  },
  {
    label: 'Portal',
    value: 'portal',
  },
];

/* Variables */
const taskStore = useTaskStore();
const { currentTask, remainingTaskCount } = storeToRefs(taskStore);

/* Computed */
const activeTab = computed(() => router?.currentRoute.value?.name as Types.TabValueType);

/* Functions */
const navigateToList = (search?: string | number) => {
  if (search) {
    router.push({ name: 'list', query: { search } });
  }
};

const getDateDisplay = (dateString?: string) => toEngDateTime(dateString!, 'yyyy-MM-dd, HH:mm:ss');

const onSelectTab = (tab: TabObject) => {
  router.push({ name: tab.value });
};
</script>

<style lang="scss" scoped>
.navbar-wrapper {
  @apply flex items-center py-2 px-4 h-[56px];

  background: linear-gradient(90deg, #eceffd 0%, #e8faf7 100%);

  .navbar-content {
    @apply flex items-center justify-between mx-auto max-w-[1280px] w-full;
  }
}

.tab-navigation-action {
  @apply flex flex-row items-center gap-[16px];
  > .tab-item:first-child {
    .tab-label {
      @apply pr-4 border-r border-gray-200 dark:border-gray-700;
    }
    .active-tab-line {
      width: calc(100% - 16px);
    }
  }
}

.tab-item {
  @apply relative cursor-pointer pb-1 text-sm font-medium transition-colors;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;

  > .active {
    @apply dark:text-gray-50;
    font-weight: 700;
    color: var(--color-primary);
  }

  > .inactive {
    @apply hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200;
    color: var(--color-text-light);
  }

  > .active-tab-line {
    @apply absolute inset-x-0 bottom-0 h-0.5 bg-blue-600 dark:bg-blue-400;
  }
}

.meta-data-items {
  @apply flex items-center space-x-4;
  font-weight: 500 !important;
  font-size: 12px !important;

  .title {
    font-weight: 700 !important;
    font-size: 12px !important;
    line-height: 24px !important;
    &:not(:last-child) {
      margin-bottom: unset !important;
    }
  }

  .meta-data-item {
    @apply flex flex-row items-center gap-1 pr-4;
    border-right: 1px solid rgba(42, 48, 73, 0.2);

    .detail {
      color: var(--color-primary);
      cursor: pointer;
    }
  }
}

.title {
  @apply font-bold;
  color: var(--color-text) !important;
}

.timestamp-wrapper {
  @apply text-left text-xs;
  .timestamp-item {
    .title {
      font-weight: 700 !important;
      font-size: 12px !important;
      color: var(--color-text) !important;
    }
    .time-value {
      @apply font-medium;
      font-weight: 500 !important;
      color: var(--color-text) !important;
    }
  }
}

.tab-navigation-wrapper {
  @apply flex h-full items-center space-x-0;
}

.task-remaining-wrapper {
  @apply px-4 py-1 rounded-full font-medium text-sm leading-5;
  background-color: var(--color-border);
}

@media (min-width: 1920px) {
  .navbar-wrapper .navbar-content {
    min-width: 1760px !important;
  }
}
</style>
