from crequest.middleware import CrequestMiddleware
from django.db import models
from django.conf import settings
from django.utils import timezone
from rest_framework.reverse import reverse
from urllib.parse import urlparse, parse_qs
from pydash import get
from ..helpers.sjcl_encryption import encrypt_url
from ..helpers.external_api import get_current_time
from ..helpers.get_form import get_form_settings
from datetime import datetime
import json
import os

EKYC_RESULT_IMAGE_TIMEOUT = settings.EKYC_RESULT_IMAGE_TIMEOUT


class DateTimeField(models.DateTimeField):
    def pre_save(self, model_instance, add):
        # to override when cloning data
        if hasattr(model_instance, "is_cloning"):
            if getattr(model_instance, "is_cloning"):
                """Return field's value just before saving."""
                return getattr(model_instance, self.attname)

        return super().pre_save(model_instance, add)


class EkycBaseItem(models.Model):
    ekyc = models.ForeignKey("Ekyc", on_delete=models.CASCADE)

    upload_response = models.CharField(max_length=191, null=True)
    result_response = models.JSONField(null=True)

    created_at = DateTimeField(auto_now_add=True)
    updated_at = DateTimeField(auto_now=True, db_index=True)

    is_cloning = None

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.updated_at = timezone.now()
        super().save(*args, **kwargs)

    @property
    def upload_response_dict(self):
        try:
            if self.upload_response is None:
                raise ValueError()
            return json.loads(self.upload_response)
        except Exception:
            return None

    @property
    def result_response_dict(self):
        return self.result_response

    @property
    def document_type(self):
        return ""

    @property
    def webhook_key_type(self):
        return ""

    @property
    def result_image_url_raw(self) -> str:
        return get(self.result_response, ["gateway_result", "image_url", "img"], "")

    @property
    def result_face_image_url_raw(self) -> str:
        return get(self.result_response, ["gateway_result", "image_url", "face_image"], "")

    @property
    def is_result_image_expired(self):
        url = self.result_image_url_raw
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        exp = get(query_params, ["exp", 0])

        current_time = get_current_time()
        img_expire_at = datetime.fromtimestamp(float(exp)) if exp else current_time

        return current_time >= img_expire_at

    def get_action_image_url(self, action_name: str) -> str | None:
        # Try to get success image
        url = get(
            self.result_response,
            ["gateway_result", "image_url", f"{action_name}_0"],
        )

        # Try to get fail image
        if not url:
            url = get(
                self.result_response,
                ["gateway_result", "image_url", f"{action_name}_fail_0"],
            )

        return url

    def build_image_url(self, raw_url, image_url):
        return encrypt_url(
            raw_url,
            obj={
                "ref": self.ekyc.ref,
                "url": image_url,
                "document_type": self.document_type,
                "request_id": self.upload_response,
            },
            timeout=EKYC_RESULT_IMAGE_TIMEOUT,
        )

    def get_result_image_url(self, is_report=False, is_extra=False, attempt="", return_face_image=False):
        from dynamicform.submodules.form.models.form import Form
        from ekyc.models import EkycBaseItem

        self: EkycBaseItem = self  # NOSONAR
        form: Form = self.ekyc.dynamicform or self.ekyc.form

        if is_extra and get_form_settings(form, "ekyc.authorize_webhook_image_url", False):
            if return_face_image:
                image_route_name = f"ekyc:report-result-image_{self.webhook_key_type}_face"
            else:
                image_route_name = f"ekyc:report-result-image_{self.webhook_key_type}"

            return reverse(
                image_route_name,
                kwargs={
                    "ekyc_ref": self.ekyc.ref,
                    "attempt": attempt,
                },
                request=CrequestMiddleware.get_request(),
            )

        if return_face_image:
            url = self.result_face_image_url_raw
        else:
            url = self.result_image_url_raw

        # Encrypt url if enabled
        if url and get_form_settings(form, "ekyc.encrypt_webhook_image_url", True):
            if is_report:
                route_name = "ekyc:report-result-image"
            else:
                route_name = "ekyc:result-image"
            raw_url = reverse(
                route_name,
                kwargs={"ekyc_ref": self.ekyc.ref},
                request=CrequestMiddleware.get_request(),
            )
            url = self.build_image_url(raw_url, url)

        return url

    def get_result_action_image_url(self, action: str, is_report=False, is_extra=False, attempt=""):
        from dynamicform.submodules.form.models.form import Form
        from ekyc.models import EkycBaseItem

        self: EkycBaseItem = self  # NOSONAR
        form: Form = self.ekyc.dynamicform or self.ekyc.form

        if is_extra and get_form_settings(form, "ekyc.authorize_webhook_image_url", False):
            return reverse(
                "ekyc:report-result-image_liveness",
                kwargs={
                    "ekyc_ref": self.ekyc.ref,
                    "attempt": attempt,
                    "action_name": action,
                },
                request=CrequestMiddleware.get_request(),
            )

        url = self.get_action_image_url(action)

        # Encrypt url if enabled
        if url and get_form_settings(form, "ekyc.encrypt_webhook_image_url", True):
            if is_report:
                route_name = "ekyc:report-result-image"
            else:
                route_name = "ekyc:result-image"
            raw_url = reverse(
                route_name,
                kwargs={"ekyc_ref": self.ekyc.ref},
                request=CrequestMiddleware.get_request(),
            )
            url = self.build_image_url(raw_url, url)

        return url

    def get_errors(self, result_path="result"):
        result = get(self.result_response, result_path, {})
        errors = []

        for key, item in result.items():
            if get(item, "status", None) is True:
                continue
            error_key = key
            error_code = get(item, "error_code", None)
            if error_code is not None:
                error_key = f"{key}_{error_code}"

            error_key = str(error_key).lower()
            errors.append(error_key)

        return errors

    def _load_error_priorities(self):
        """Load error priorities from JSON file"""
        try:
            json_path = os.path.join(
                settings.BASE_DIR, "project", "frontend", "src", "helpers", "ekyc-error-priority.json"
            )
            with open(json_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # Fallback to empty dict if file not found or invalid JSON
            return {}

    def get_sorted_errors(self, result_path="result", document_type=None):
        """
        Get sorted errors based on priority from JSON configuration file.

        Args:
            result_path (str): Path to the result in result_response
            document_type (str, optional): Document type to get priority for from JSON file.
            If not provided, will try to use self.document_type.

        Returns:
            list: Sorted list of error keys
        """
        errors = self.get_errors(result_path=result_path)

        # Load priorities from JSON file
        error_priorities = self._load_error_priorities()

        # Determine document type
        doc_type = document_type
        if doc_type is None and hasattr(self, "document_type"):
            doc_type = self.document_type

        # Get priority list for the document type
        error_priority = error_priorities.get(doc_type, [])

        sorted_errors = []
        for error_key in error_priority:
            if error_key in errors:
                sorted_errors.append(error_key)
                errors.remove(error_key)
        sorted_errors = sorted_errors + errors

        return sorted_errors
