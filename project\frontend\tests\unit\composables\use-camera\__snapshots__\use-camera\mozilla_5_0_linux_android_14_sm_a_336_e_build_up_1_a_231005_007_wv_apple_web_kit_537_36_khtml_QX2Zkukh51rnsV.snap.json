[{"deviceMethod": "match_label", "resolutionMethod": "best", "constraints": {"video": {"deviceId": {"exact": "$D3"}, "width": {"ideal": 1920, "min": 320}, "height": {"ideal": 1080, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "match_label", "resolutionMethod": "worst", "constraints": {"video": {"facingMode": "environment", "width": {"min": 320}, "height": {"min": 240}}, "audio": false}, "streamTracks": null}, {"deviceMethod": "match_label", "resolutionMethod": "scale", "constraints": {"video": {"deviceId": {"exact": "$D3"}, "width": {"ideal": 1920, "min": 320}, "height": {"ideal": 1080, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "match_label", "resolutionMethod": "scale", "constraints": {"video": {"deviceId": {"exact": "$D3"}, "width": {"ideal": 1440, "min": 320}, "height": {"ideal": 810, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "match_label", "resolutionMethod": "scale", "constraints": {"video": {"deviceId": {"exact": "$D3"}, "width": {"ideal": 1600, "min": 320}, "height": {"ideal": 900, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "match_label", "resolutionMethod": "scale", "constraints": {"video": {"deviceId": {"exact": "$D3"}, "width": {"ideal": 1366, "min": 320}, "height": {"ideal": 768, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "match_label", "resolutionMethod": "scale", "constraints": {"video": {"deviceId": {"exact": "$D3"}, "width": {"ideal": 1280, "min": 320}, "height": {"ideal": 720, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "default", "resolutionMethod": "best", "constraints": {"video": {"width": {"ideal": 1920, "min": 320}, "height": {"ideal": 1080, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "default", "resolutionMethod": "worst", "constraints": {"video": {"facingMode": "environment", "width": {"min": 320}, "height": {"min": 240}}, "audio": false}, "streamTracks": null}, {"deviceMethod": "default", "resolutionMethod": "scale", "constraints": {"video": {"width": {"ideal": 1920, "min": 320}, "height": {"ideal": 1080, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "default", "resolutionMethod": "scale", "constraints": {"video": {"width": {"ideal": 1440, "min": 320}, "height": {"ideal": 810, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "default", "resolutionMethod": "scale", "constraints": {"video": {"width": {"ideal": 1600, "min": 320}, "height": {"ideal": 900, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "default", "resolutionMethod": "scale", "constraints": {"video": {"width": {"ideal": 1366, "min": 320}, "height": {"ideal": 768, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}, {"deviceMethod": "default", "resolutionMethod": "scale", "constraints": {"video": {"width": {"ideal": 1280, "min": 320}, "height": {"ideal": 720, "min": 240}, "facingMode": "environment"}, "audio": false}, "streamTracks": null}]