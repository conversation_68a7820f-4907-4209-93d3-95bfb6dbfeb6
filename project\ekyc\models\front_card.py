from django.db import models
from pydash import get, set_

from .ekyc_base_item import EkycBaseItem
from rest_framework import status
from time import sleep
from ..helpers.external_api import *  # NOSONAR
from ..helpers.get_form import check_sandbox_mode, check_authorize_webhook_image_url
from ..helpers.document_checker import check_by_schema_conditions


class FrontCard(EkycBaseItem):
    is_success = models.BooleanField(default=False, db_index=True)
    nonce = models.CharField(max_length=5, db_index=True, null=True)

    @property
    def document_type(self):
        try:
            if self.result_response is None:
                raise ValueError()
            result = self.result_response
            return get(result, ["document_type"], "front_card")
        except Exception:
            return "front_card"

    @property
    def webhook_key_type(self):
        return "identity_document"

    @property
    def is_nfc_success(self):
        return get(self.result_response, "nfc", True)

    @property
    def warning(self) -> dict:
        return get(self.result_response, ["warning"], {})

    def check_conditions(self, schema=None):
        try:
            result = self.result_response
            if result is None:
                return False

            pass_all_checks, _, _ = check_by_schema_conditions(schema=schema, response=result, with_ocr=False)
            return pass_all_checks
        except Exception:
            return False

    def check_nfc(self, schema=None):
        from .nfc import Nfc, NFC_STATUS_SUCCESS

        try:
            # Not passport = always pass
            if self.document_type not in ["passport"]:
                return True

            nfc: Nfc = self.nfc_set.order_by("-updated_at").first()

            # NFC not needed = pass
            if not nfc and not get(schema, "include_nfc", False):
                return True

            # has SUCCESS NFC = pass
            if nfc and nfc.status == NFC_STATUS_SUCCESS:
                return True

            if self.is_success:
                self.is_success = False
                self.result_response = set_(self.result_response, "nfc", False)
                self.save(update_fields=["is_success", "result_response"])
            return False
        except Exception:
            return False

    def fetch_report(self, is_extra=False, process_logs=None, **kwargs):
        if process_logs is None:
            process_logs = []

        # No request id
        if not self.upload_response:
            return None, None

        # Current result
        result = get(self.result_response, "gateway_result")
        api_status = get(self.result_response, "gateway_api_status")

        # SANDBOX -> skip
        process_logs.append(["check_sandbox_mode", str(datetime.now().isoformat())])
        if check_sandbox_mode(self.ekyc.ref):
            return result, api_status

        process_logs.append(["check_authorize_webhook_image_url", str(datetime.now().isoformat())])
        if is_extra and check_authorize_webhook_image_url(self.ekyc.ref):
            return result, api_status

        # Fresh image -> skip
        process_logs.append(["check_fresh_image", str(datetime.now().isoformat())])
        if self.result_response and not self.is_result_image_expired:
            return result, api_status

        # Refresh result
        success = False
        get_result_method_to_call = globals()[f"get_result_{self.document_type}"]
        for i in range(5, 0, -1):
            process_logs.append(["call_gateway", str(datetime.now().isoformat()), i])
            result, api_status = get_result_method_to_call(self.upload_response)
            updated_result = self.result_response
            updated_result["gateway_result"] = result
            updated_result["gateway_api_status"] = api_status
            if status.is_success(api_status):
                process_logs.append(["gateway_success", str(datetime.now().isoformat())])
                success = True
                break
            elif i > 1:
                sleep(2)
            else:
                process_logs.append(["gateway_timeout", str(datetime.now().isoformat())])
                raise TimeoutError("Error getting result, Timeout, Try again later")

        # End retry loop
        key = "gateway_last_success_at" if success else "gateway_last_fail_at"
        updated_result[key] = get_current_time().isoformat(" ")

        process_logs.append(["update_result", str(datetime.now().isoformat())])
        FrontCard.objects.filter(id=self.id).update(result_response=updated_result)
        # NOTE: Not using self.save() to prevent sending post_save
        # self.result_response = updated_result
        # self.save()

        return result, api_status

    def get_latest_error(self):
        result_response = get(self.result_response, "data.result", {})
        latest_error = []
        for key, i in result_response.items():
            if i.get("status", None) == False:
                latest_error.append({"message": i.get("message", None), "key": key})
        return latest_error

    def call_webhook(self, is_success=None):
        if not is_success:
            is_success = self.is_success
        hook_to_call = "front_card_success" if is_success else "front_card_failed"
        self.ekyc.call_webhook(hook_to_call, payload={"attempt": self.ekyc.front_card_attempt_count})

    def get_attempt_sorted_errors(self):
        return self.get_sorted_errors(result_path="data.result", document_type=self.document_type)
