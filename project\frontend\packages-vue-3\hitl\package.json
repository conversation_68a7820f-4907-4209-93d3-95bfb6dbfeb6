{"name": "hitl", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build && node post-build.js", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/vite": "^4.1.14", "@tanstack/vue-table": "^8.21.3", "@vueuse/core": "^13.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash": "^4.17.21", "lucide-vue-next": "^0.546.0", "pinia": "^3.0.1", "reka-ui": "^2.5.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.14", "tw-animate-css": "^1.4.0", "vue": "^3.5.22", "vue-router": "^4.6.3", "vue-sonner": "^2.0.9"}, "types": "src/types/index.d.ts", "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^24.8.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.8.1", "eslint": "^9.22.0", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "typescript": "~5.9.3", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^30.0.0", "vite": "npm:rolldown-vite@7.1.14", "vitest": "^3.0.8", "vue-tsc": "^3.1.0"}, "resolutions": {"vite": "npm:rolldown-vite@7.1.14"}}