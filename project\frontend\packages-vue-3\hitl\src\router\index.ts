import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';

import ListView from '@/views/ListView.vue';
import PortalView from '@/views/PortalView.vue';

import routerSessionChecker from './router-session-checker.ts';

const routes: RouteRecordRaw[] = [
  {
    path: '/hitl',
    name: 'hitl',
    redirect: '/hitl/portal',
    children: [
      {
        path: 'list',
        name: 'list',
        component: ListView,
      },
      {
        path: 'portal/:taskId?',
        name: 'portal',
        component: PortalView,
        props: true,
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

routerSessionChecker(router);

export default router;
