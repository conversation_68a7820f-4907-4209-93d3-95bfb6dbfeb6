<template>
  <div
    class="preview-image-wrapper"
    :class="[
      media,
      {
        'is-success': state?.success && !localIsLoading,
        'is-fail': !state?.success && !localIsLoading,
        'is-loading': localIsLoading,
      },
    ]"
  >
    <!-- Loader -->
    <EkycLoadingOverlay
      v-show="localIsLoading"
      ref="uploadingOverlayRef"
      :progress="mediaData.progress"
      :show-subtitle="false"
      class="loading-overlay"
    />

    <!-- Result Header -->
    <div v-if="!localIsLoading" class="result-header">
      <div class="result-icon">
        <ReactiveIcon :icon="state?.success ? 'lucide:check' : 'lucide:x'" width="24" height="24" />
      </div>
      <span class="result-title"> {{ displayMessage.title }} </span>
    </div>

    <!-- Result Image wrapper-->
    <div :class="{ 'loaded-content-border': !localIsLoading }">
      <!-- Image preview -->
      <div class="image-frame">
        <img v-show="state?.url" :src="state?.url" alt="taken document" class="preview-image" />
      </div>

      <!-- Result Description -->
      <div v-if="!state?.success && !localIsLoading" class="result-desc">
        <span class="result-desc-text"> {{ displayMessage.desc }} </span>
        <div v-if="enabledNotice" @click="emit('trigger-notice')">
          <ReactiveIcon
            icon="lucide:circle-question-mark"
            width="20"
            height="20"
            class="result-desc-icon"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { templateRef, until } from '@vueuse/core';
import { animate } from 'motion';

import useDynamicForm from '@core/composables/use-dynamic-form';

import ReactiveIcon from '@helpers/components/ReactiveIcon.vue';
import { useI18n } from '@helpers/helpers/vue-i18n-composable';

import EkycLoadingOverlay from '@ekyc/components/EkycLoadingOverlay/EkycLoadingOverlay.vue';
import { useEkycStore } from '@ekyc/store/modules/ekyc';

import { Props, useEkycFailManager } from '../use-ekyc-fail-manager';

/* Types */
type AnimationPlaybackControls = ReturnType<typeof animate>;

/* Emits */
const emit = defineEmits<(e: 'trigger-notice') => void>();

/* Props */
const props = defineProps({
  ...Props,
  state: {
    type: Object as () => Types.PreviewType,
    required: true,
  },
  isLoading: {
    type: Boolean,
    required: false,
    default: false,
  },
});

/* Variables */
const uploadingOverlayRef = templateRef('uploadingOverlayRef');

const { t } = useI18n();
const { failedErrorToShow, resultState, failKeys } = useEkycFailManager(props);

const ekycStore = useEkycStore();
const mediaData = computed(() => ekycStore.getMediaData(props.media));

const { dynamicFormInstance } = useDynamicForm(inject<string>('id'));

let blurAnimation: AnimationPlaybackControls;

/* Computed */
const localIsLoading = ref<boolean>(false);
const displayMessage = computed(() =>
  props.state?.success ? { title: t('ekyc.recorder.card_passed'), desc: '' } : failedErrorToShow,
);
const enabledNotice = computed(
  () =>
    props.isAutoDetectDocumentType &&
    ['failed_backend', 'failed_others'].includes(resultState.value) &&
    failKeys.value.includes('supported_document_type'),
);

/* Functions */
const startLoading = async () => {
  localIsLoading.value = true;

  await until(uploadingOverlayRef).toBeTruthy({ timeout: 5_000 });

  uploadingOverlayRef.value?.start?.();

  const imgEl = document.querySelector(
    `.${props.media} .image-frame .preview-image`,
  ) as HTMLDivElement;

  if (imgEl) {
    blurAnimation = animate(10, 0, {
      duration: 10,
      onUpdate: v => {
        imgEl.style.filter = `blur(${v.toFixed(2)}px)`;
      },
    });
  }
};

const endProgress = async () => {
  try {
    uploadingOverlayRef.value.complete().then(() => {
      localIsLoading.value = false;
    });

    if (blurAnimation) {
      blurAnimation.speed = 10;
      blurAnimation.then(() => {
        blurAnimation = undefined;
      });
    }
  } catch {
    localIsLoading.value = false;
  }
};

/* Watcher */
watch(
  () => props.isLoading,
  async (val, oldVal) => {
    // Start loading document
    if (!oldVal && val) {
      startLoading();
    }
    // Stop loading document
    if (oldVal && !val) {
      endProgress();
    }
  },
  { immediate: true },
);

defineExpose({ resultState, dynamicFormInstance, failKeys });
</script>

<style scoped lang="scss">
.notice-message > .notice-message__text {
  @apply text-sm font-medium;
  color: var(--color-text-light, #777b8b);
}

.preview-image-wrapper {
  // aspect-ratio: 1.53;
  width: 100%;
  max-width: 382px;
  overflow: hidden;
  position: relative;
  height: 100%;
  min-width: 100%;
  min-height: 100%;
}

.result-desc {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
  width: 100%;
  padding: 12px;
  padding-left: 8px;
  color: #303c46;

  background-color: rgb(from var(--color-danger) r g b / 0.2);
  .result-desc-icon {
    color: var(--color-danger);
    min-width: 24px;
    min-height: 24px;
    cursor: pointer;
  }
  .result-desc-text {
    @apply w-full text-start;

    display: inline-block;
    overflow-wrap: break-word;
    word-break: break-word;

    font-weight: 400;
    font-size: 13px;
    line-height: 24px;
  }
}

.result-header {
  min-height: 40px;
  display: flex;
  justify-content: flex-start;
  color: white;
  text-align: left;

  border-radius: 12px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;

  .result-icon {
    @apply flex items-center justify-center;
    flex-shrink: 0;
    width: 40px;
    min-width: 40px;
    min-height: 40px;
    background-color: rgba(42, 48, 73, 0.4);
    border-top-left-radius: 12px;
  }

  .result-title {
    @apply py-2 px-3 font-medium text-base leading-6 h-full;

    display: inline-block;
    overflow-wrap: break-word;
    word-break: break-word;
    flex-grow: 1;
    min-width: 0;
  }
}

.loaded-content-border {
  border: 4px solid transparent;
  border-top-width: 0;
  border-radius: 12px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.is-success {
  .loaded-content-border {
    border-color: var(--color-success);
  }
  .result-header {
    background-color: var(--color-success);
  }
}

.is-fail {
  .image-frame,
  .preview-image {
    border-radius: unset;
  }
  .loaded-content-border {
    border-color: var(--color-danger);
  }
  .result-header {
    background-color: var(--color-danger);
  }
}

.is-loading .image-frame {
  border-radius: 12px;
}

.image-frame {
  aspect-ratio: 4 / 3;
  height: 100%;
  width: 100%;
  background-color: #e5e7eb;
  overflow: hidden;
}

.is-success .image-frame {
  background-color: rgb(from var(--color-success) r g b / 0.1);
}

.is-fail .image-frame {
  background-color: rgb(from var(--color-danger) r g b / 0.1);
}

.is-fail .preview-image {
  object-fit: contain;
}

.preview-image {
  height: 100%;
  width: 100%;
  object-fit: cover;

  border-radius: 8px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.is-loading {
  .preview-image,
  .image-frame {
    border-radius: 12px;
  }
}

.loading-overlay {
  @apply bg-[#FFFFFF99] p-4 rounded-2xl;
  @apply w-36 h-36 m-auto;
  @apply gap-2;
}
</style>
