import type { MaybeRef } from '@vueuse/core';
import throttle from 'lodash/throttle';
import { type AnimationOptions, type At, animate, stagger } from 'motion';

type Options = AnimationOptions & At;

type AnimationPlaybackControls = ReturnType<typeof animate>;

const FOCUS_MASK_BACKGROUND = '#000f';
const FOCUS_MASK_BACKGROUND_RGB = 'rgb(0, 0, 0)';
export const forcedShowTarget = ref<string[]>([]);

const SHOW = { opacity: 100 } as const;
const HIDE = { opacity: 0 } as const;

// Global variables to control the start instruction animation
let startInstructionAnimationControls: AnimationPlaybackControls | null = null;

function changeUiColor(uiState: 'normal' | 'success' | 'warn' | 'danger') {
  const color = {
    normal: '#FFF',
    success: 'var(--color-success, #19CEB0)',
    warn: 'var(--color-warning, #FFE27C)',
    danger: 'var(--color-danger, #ED6898)',
  }[uiState];

  const maskEl: HTMLDivElement = document.querySelector('#face-mask-rect-face-svg-mask');
  if (maskEl) {
    maskEl.style.borderColor = color;
  }

  const topText: HTMLElement = document.querySelector('#top-texts-container');
  const progressColor1: SVGStopElement = document.querySelector('#progress-color-1');
  const progressColor2: SVGStopElement = document.querySelector('#progress-color-2');
  if (topText && progressColor1 && progressColor2) {
    animate(topText.style.color || '#fff', color, {
      duration: 0,
      onUpdate: v => {
        topText.style.color = v;
        progressColor1.style.stopColor = v;
        progressColor2.style.stopColor = v;
      },
    });
  }
}

async function showTopTextByKey(
  key:
    | 'prepare-liveness'
    | 'detecting-face-position'
    | 'align-your-face'
    | 'press-button'
    | 'scan-complete'
    | 'action-failed'
    | 'error-message',
) {
  await animate([
    [
      `#top-texts-container > :not(#${key})`,
      { opacity: 0, filter: ['blur(1px)', 'blur(0px)'] },
      { ease: 'easeIn', duration: 0 },
    ],
    [
      `#top-texts-container > #${key}`,
      { opacity: 1, filter: ['blur(1px)', 'blur(0px)'] },
      { ease: 'easeIn', duration: 0 },
    ],
  ]).then(() => {});

  if (key === 'detecting-face-position') {
    animate([
      [
        `#top-texts-container > #${key} .dot`,
        { opacity: [0, 1] },
        { ease: 'easeIn', delay: stagger(0.5), duration: 0 },
      ],
    ]);
  }
}

function focusMask() {
  const maskEl: HTMLDivElement = document.querySelector('#face-mask-rect-face-svg-mask');
  if (maskEl) {
    maskEl.classList.add('focused');
  }
}

export async function playStartInstruction(options: Options = {}) {
  showTopTextByKey('prepare-liveness');

  let container = document.getElementById('start-instructions-container');
  if (!container) return;

  const instructions = container.querySelectorAll('.start-instruction');

  // Store animation controls
  startInstructionAnimationControls = animate([
    // Instructions
    ['#skip-instruction-btn', SHOW, { ease: 'easeOut', duration: 0, ...options }],
    [container, SHOW, { ease: 'easeOut', delay: stagger(0), duration: 0, ...options }],
    [
      instructions,
      {
        y: [-5, 0],
        display: 'flex',
      },
      { ease: 'easeOut', delay: stagger(0), duration: 0, ...options },
    ],
    // HR
    [
      container.querySelector('hr'),
      { y: [-5, 0], display: 'flex' },
      { ease: 'easeOut', duration: 0, at: 0, ...options },
    ],
  ]);

  // Wait for the animation to finish naturally or be finished by skip
  await startInstructionAnimationControls?.then(() => {});

  // Clear controls once done
  startInstructionAnimationControls = null;
}

export function skipStartInstruction(options: Options = {}) {
  startInstructionAnimationControls?.complete();

  showTopTextByKey('detecting-face-position');

  // Hide instructions
  document.getElementById('start-instructions-container').style.opacity = '0';
  document.getElementById('instruction-dim-bg')?.setAttribute('style', 'opacity: 0');
  document.getElementById('skip-instruction-btn')?.setAttribute('style', 'opacity: 0');
  document.getElementById('face-mask-svg')?.setAttribute('style', 'opacity: 1');
}

export const playManualSnap = async () => {
  console.log('playManualSnap');

  changeUiColor('normal');
  hideIndicators();
  showTopTextByKey('press-button');
  focusMask();
};

export async function playRetryLiveness() {
  console.log('playRetryLiveness');

  changeUiColor('normal');
  hideIndicators();
  showTopTextByKey('align-your-face');

  const mask = document.getElementById('face-mask-svg');
  if (mask) {
    animate(mask, SHOW, { duration: 0.1 });
  }

  focusMask();
}

export async function playStartChecking() {
  console.log('playStartChecking');

  changeUiColor('normal');
  hideIndicators();

  showTopTextByKey('align-your-face');

  focusMask();
}

async function hideIndicators(
  targets = [
    '#action-progression',
    '#success-indicator',
    '#fail-indicator',
    '#warn-indicator',
    '#action-indicator',
  ],
) {
  const el = document.querySelectorAll(
    targets.filter(t => !forcedShowTarget.value?.includes(t)).join(', '),
  );
  if (el.length) {
    return animate(el, { y: 20, opacity: 0 }, { duration: 0 }).then(() => {});
  }
}

export async function showIndicator(id: string) {
  const el = document.getElementById(id);
  if (el) {
    return animate([[el, { y: [20, 0], opacity: 100 }, { duration: 0 }]]).then(() => {});
  }
}

export async function playSuccessIndicator() {
  changeUiColor('success');
  showTopTextByKey('scan-complete');

  await hideIndicators();
  showIndicator('success-indicator');
}

export async function playFailIndicator() {
  changeUiColor('danger');
  showTopTextByKey('action-failed');

  await hideIndicators();
  showIndicator('fail-indicator');
}

export async function playWarnIndicator() {
  changeUiColor('warn');
  showTopTextByKey('error-message');

  await hideIndicators();
  showIndicator('warn-indicator');
}

export async function playActionIndicator() {
  changeUiColor('normal');
  showTopTextByKey('align-your-face');
  await hideIndicators();

  showIndicator('action-indicator');
}

let donutProgressController: ReturnType<typeof animate> = null;

export async function playProgressLivenessDonutReverse(duration = 1) {
  const el: SVGAElement = document.querySelector('.liveness-action-progress .foreground');

  if (!el) {
    return;
  }

  if (donutProgressController) {
    donutProgressController.stop();
  }

  donutProgressController = animate(0, 1, {
    duration,
    delay: 0.1,
    repeat: 0,
    onUpdate: v => {
      if (el) {
        el.style.strokeDashoffset = v.toFixed(2);
      }
    },
  });
  return donutProgressController.then(() => {});
}

export async function playProgressLivenessDonut(progress: number = 100) {
  const el: SVGAElement = document.querySelector('.liveness-action-progress .foreground');
  donutProgressController = animate(1, 1.01 - progress / 100, {
    duration: 1,
    delay: 0.2,
    repeat: 0,
    onUpdate: v => {
      if (el) {
        el.style.strokeDashoffset = v.toFixed(2);
      }
    },
  });
  return donutProgressController.then(() => {});
}

async function startProgressLiveness() {
  console.log('playProgressLiveness');

  changeUiColor('normal');
  showTopTextByKey('align-your-face');

  await hideIndicators();
  await animate('#action-progression', { y: [20, 0], opacity: 100 }, { duration: 0 }).then(
    () => {},
  );

  if (donutProgressController === null) {
    playProgressLivenessDonut();
  }
}

export async function stopProgressLiveness() {
  if (forcedShowTarget.value.includes('#action-progression')) {
    return;
  }
  hideIndicators();
  donutProgressController?.cancel?.();
  donutProgressController = null;
}

export async function playShowOverlay() {
  console.log('playShowOverlay');

  changeUiColor('normal');
  await hideIndicators();
}

export function prepareProgressUpload(rootEl: HTMLElement) {
  console.log('prepareProgressUpload');

  hideIndicators();

  const mask = document.getElementById('face-mask-svg');
  if (mask) {
    animate(mask, HIDE, { duration: 0.1 });
  }

  const uploading1 = rootEl.querySelector('.uploading-1');
  const verifying1 = rootEl.querySelector('.verifying-1');
  const verifying2 = rootEl.querySelector('.verifying-2');
  if (uploading1 && verifying1 && verifying2) {
    animate([
      [uploading1, { opacity: 100 }, { ease: 'easeOut', duration: 0 }],
      [verifying1, { opacity: 0 }, { ease: 'easeOut', duration: 0 }],
      [verifying2, { opacity: 0 }, { ease: 'easeOut', duration: 0 }],
    ]);
  }
}

export function playProgressUploadStage1(rootEl: HTMLElement, progressRef: MaybeRef<number>) {
  console.log('playProgressUploadStage1');

  const DURATION = 60; // sec
  const SPEED_FAST = 20;
  const SPEED_SLOW = 1;

  let finished = false;

  const el: SVGAElement = rootEl.querySelector('.uploading-overlay .foreground');
  const controller = animate(1, 0.5, {
    duration: DURATION,
    ease: 'linear',
    onUpdate: v => {
      if (el) {
        el.style.strokeDashoffset = v.toFixed(3);

        const progress = +unref(progressRef);
        if (progress >= 100) {
          finished = true;
        }

        if (finished) {
          if (controller.speed < SPEED_FAST) {
            controller.speed = SPEED_FAST;
          }
          return;
        }

        const newTime = (DURATION * progress) / 100;
        if (newTime > controller.time) {
          controller.speed = SPEED_FAST;
        } else {
          controller.speed = SPEED_SLOW;
        }
      }
    },
  });

  return controller;
}

export function playProgressUploadStage2(rootEl: HTMLElement) {
  console.log('playProgressUploadStage2');

  if (!rootEl) {
    return null;
  }

  const uploading1 = rootEl.querySelector('.uploading-1');
  const verifying1 = rootEl.querySelector('.verifying-1');
  const verifying2 = rootEl.querySelector('.verifying-2');
  if (uploading1 && verifying1 && verifying2) {
    animate([
      [uploading1, { opacity: 0 }, { ease: 'easeOut', duration: 0.2 }],
      [verifying1, { opacity: 100 }, { ease: 'easeOut', duration: 0.2 }],
      [verifying1, { opacity: 0 }, { ease: 'easeOut', duration: 0.2, delay: 2 }],
      [verifying2, { opacity: 100 }, { ease: 'easeOut', duration: 0.2 }],
    ]);
  }

  const DURATION = 10; // sec

  const el: SVGAElement = rootEl.querySelector('.uploading-overlay .foreground');
  const controller = animate(0.5, 0.001, {
    duration: DURATION,
    ease: 'easeOut',
    onUpdate: v => {
      if (el) {
        el.style.strokeDashoffset = v.toFixed(3);
      }
    },
  });

  return controller;
}

// Create a throttled animation manager
let animationInProgress = false;
let pendingAnimation: { name: ANIMATION_NAME; options: Options } = null;

const ANIMATION_MAP = {
  startInstruction: throttle(playStartInstruction, 300),
  manualSnap: throttle(playManualSnap, 300),
  retryLiveness: throttle(playRetryLiveness, 300),
  startChecking: throttle(playStartChecking, 300),
  progressLiveness: throttle(startProgressLiveness, 300),
  stopProgressLiveness: throttle(stopProgressLiveness, 300),
  successIndicator: throttle(playSuccessIndicator, 300),
  failIndicator: throttle(playFailIndicator, 300),
  warnIndicator: throttle(playWarnIndicator, 300),
  actionIndicator: throttle(playActionIndicator, 300),
  showOverlay: throttle(playShowOverlay, 300),
};
type ANIMATION_NAME = keyof typeof ANIMATION_MAP;

async function _playAnimation(animationName: ANIMATION_NAME, options: Options = {}) {
  if (animationInProgress) {
    pendingAnimation = { name: animationName, options };
    return;
  }

  animationInProgress = true;

  // Execute the animation
  const animationFn = ANIMATION_MAP[animationName];
  if (animationFn) {
    await animationFn(options);
  }

  animationInProgress = false;

  // Play any pending animation
  if (pendingAnimation) {
    const { name, options } = pendingAnimation;
    pendingAnimation = null;
    playAnimation(name, options);
  }
}

// Export the centralized animation function
export function playAnimation(animationName: ANIMATION_NAME, options: Options = {}) {
  // console.log(`Requesting animation: ${animationName}`);
  return _playAnimation(animationName, options);
}
